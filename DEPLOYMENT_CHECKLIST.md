# CVBioLabs Production Deployment Checklist

Use this checklist to ensure a complete and secure production deployment.

## Pre-Deployment Setup

### DigitalOcean Droplet Setup
- [ ] Create DigitalOcean droplet (minimum 2GB RAM, 2 vCPUs)
- [ ] Choose Ubuntu 20.04+ LTS
- [ ] Setup SSH key authentication
- [ ] Configure firewall (UFW)
- [ ] Update system packages: `sudo apt update && sudo apt upgrade -y`

### Domain Configuration
- [ ] Point domain A record to droplet IP address
- [ ] Point www CNAME to main domain
- [ ] Verify DNS propagation: `nslookup yourdomain.com`

### Server Preparation
- [ ] Install Docker: `curl -fsSL https://get.docker.com -o get-docker.sh && sudo sh get-docker.sh`
- [ ] Install Docker Compose
- [ ] Add user to docker group: `sudo usermod -aG docker $USER`
- [ ] Clone repository to server
- [ ] Install additional tools: `sudo apt install -y ufw fail2ban certbot`

## Configuration

### Environment Setup
- [ ] Copy `.env.production.template` to `.env.production`
- [ ] Generate strong SECRET_KEY (32+ characters)
- [ ] Set secure database passwords
- [ ] Configure email settings (SMTP)
- [ ] Set up Razorpay credentials (if using payments)
- [ ] Update domain name in configuration
- [ ] Set Redis password

### Security Configuration
- [ ] Change default SSH port (optional but recommended)
- [ ] Disable root login via SSH
- [ ] Setup fail2ban for intrusion prevention
- [ ] Configure UFW firewall rules
- [ ] Generate strong passwords for all services

### SSL Certificate Setup
- [ ] Ensure domain points to server
- [ ] Run deployment script with domain: `./deploy.sh yourdomain.com <EMAIL>`
- [ ] Verify SSL certificate installation
- [ ] Test HTTPS redirect

## Deployment

### Initial Deployment
- [ ] Make deployment script executable: `chmod +x deploy.sh`
- [ ] Run deployment script: `./deploy.sh yourdomain.com <EMAIL>`
- [ ] Monitor deployment logs for errors
- [ ] Verify all services are running: `docker-compose ps`

### Database Setup
- [ ] Run database initialization: `python3 init_production_db.py`
- [ ] Create admin user account
- [ ] Test database connectivity
- [ ] Import any existing data (if applicable)

### Application Testing
- [ ] Test website accessibility: `https://yourdomain.com`
- [ ] Test health endpoint: `https://yourdomain.com/health`
- [ ] Test admin login functionality
- [ ] Test email functionality
- [ ] Test file upload functionality
- [ ] Test payment gateway (if applicable)

## Post-Deployment

### Monitoring Setup
- [ ] Setup monitoring script: `chmod +x production_monitor.py`
- [ ] Test monitoring: `python3 production_monitor.py`
- [ ] Configure monitoring cron job
- [ ] Setup log rotation
- [ ] Configure backup system

### Backup Configuration
- [ ] Make backup script executable: `chmod +x backup.sh`
- [ ] Test backup script: `./backup.sh`
- [ ] Setup automated daily backups
- [ ] Test backup restoration process
- [ ] Configure backup retention policy

### Security Hardening
- [ ] Review and update firewall rules
- [ ] Setup intrusion detection (fail2ban)
- [ ] Configure rate limiting
- [ ] Review application logs for security issues
- [ ] Setup security monitoring alerts

### Performance Optimization
- [ ] Monitor resource usage
- [ ] Optimize database settings if needed
- [ ] Configure CDN (if applicable)
- [ ] Setup caching (Redis is already configured)
- [ ] Monitor response times

## Ongoing Maintenance

### Daily Tasks
- [ ] Check application health: `./production_monitor.py`
- [ ] Review error logs: `docker-compose logs web`
- [ ] Monitor resource usage
- [ ] Verify backup completion

### Weekly Tasks
- [ ] Update system packages: `sudo apt update && sudo apt upgrade`
- [ ] Review security logs
- [ ] Check SSL certificate expiration
- [ ] Review database performance
- [ ] Clean up old log files

### Monthly Tasks
- [ ] Review and rotate passwords
- [ ] Update application dependencies
- [ ] Review backup retention policy
- [ ] Security audit and penetration testing
- [ ] Performance optimization review

## Troubleshooting

### Common Issues

**Services not starting:**
```bash
docker-compose logs [service_name]
docker-compose restart [service_name]
```

**Database connection issues:**
```bash
docker-compose exec db mysql -u root -p
```

**SSL certificate issues:**
```bash
sudo certbot renew
docker-compose restart nginx
```

**High resource usage:**
```bash
docker stats
htop
```

### Emergency Procedures

**Complete system restart:**
```bash
docker-compose down
docker-compose up -d
```

**Database recovery:**
```bash
# Restore from backup
docker-compose exec db mysql -u root -p cvbiolabs < backup_file.sql
```

**Application rollback:**
```bash
git checkout [previous_commit]
docker-compose build
docker-compose up -d
```

## Contact Information

- **System Administrator:** [Your Name]
- **Email:** [<EMAIL>]
- **Emergency Contact:** [Phone Number]
- **Hosting Provider:** DigitalOcean
- **Domain Registrar:** [Your Domain Registrar]

## Important Files and Locations

- **Application Code:** `/home/<USER>/cvbiolabs/`
- **Environment Config:** `.env.production`
- **SSL Certificates:** `/etc/letsencrypt/live/yourdomain.com/`
- **Backups:** `/backup/cvbiolabs/`
- **Logs:** `./logs/`
- **Uploads:** `./uploads/`

## Support Resources

- **Documentation:** `PRODUCTION_DEPLOYMENT.md`
- **Monitoring:** `python3 production_monitor.py`
- **Backup:** `./backup.sh`
- **Health Check:** `https://yourdomain.com/health`

---

**Last Updated:** $(date)
**Deployment Version:** 1.0
**Checklist Completed By:** ________________
**Date Completed:** ________________
