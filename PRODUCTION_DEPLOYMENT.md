# CVBioLabs Production Deployment Guide

This guide will help you deploy CVBioLabs to DigitalOcean with your domain and SSL certificates.

## Prerequisites

- DigitalOcean Droplet (Ubuntu 20.04+ recommended, minimum 2GB RAM)
- Domain name pointed to your server's IP address
- SSH access to your server

## Quick Start

1. **Clone the repository** on your DigitalOcean server:
   ```bash
   git clone <your-repo-url> cvbiolabs
   cd cvbiolabs
   ```

2. **Install Docker and Docker Compose**:
   ```bash
   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   sudo usermod -aG docker $USER
   
   # Install Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   
   # Log out and back in for group changes to take effect
   ```

3. **Configure environment**:
   ```bash
   cp .env.production.template .env.production
   nano .env.production  # Edit with your actual values
   ```

4. **Run deployment script**:
   ```bash
   chmod +x deploy.sh
   ./deploy.sh yourdomain.com <EMAIL>
   ```

## Detailed Configuration

### Environment Variables (.env.production)

**Critical settings to change:**

```env
# Generate a strong secret key
SECRET_KEY=your-super-secret-key-here

# Database credentials
DB_PASSWORD=your-secure-database-password
DB_ROOT_PASSWORD=your-secure-root-password

# Redis password
REDIS_PASSWORD=your-secure-redis-password

# Email configuration
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password

# Payment gateway
RAZORPAY_KEY_ID=your-razorpay-key-id
RAZORPAY_KEY_SECRET=your-razorpay-key-secret

# Domain
DOMAIN_NAME=yourdomain.com
```

### Domain Configuration

1. **Point your domain to your server**:
   - Add an A record pointing to your DigitalOcean droplet's IP
   - Add a CNAME record for www pointing to your domain

2. **DNS propagation**: Wait for DNS to propagate (can take up to 24 hours)

### SSL Certificate Setup

The deployment script automatically:
- Installs Certbot
- Generates Let's Encrypt SSL certificates
- Sets up automatic renewal

Manual SSL setup if needed:
```bash
sudo certbot certonly --standalone -d yourdomain.com -d www.yourdomain.com
```

## Security Hardening

### Firewall Setup
```bash
# Install UFW
sudo apt install ufw

# Allow SSH, HTTP, and HTTPS
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443

# Enable firewall
sudo ufw enable
```

### Additional Security Measures

1. **Change SSH port** (optional but recommended):
   ```bash
   sudo nano /etc/ssh/sshd_config
   # Change Port 22 to Port 2222
   sudo systemctl restart ssh
   sudo ufw allow 2222
   sudo ufw delete allow ssh
   ```

2. **Disable root login**:
   ```bash
   sudo nano /etc/ssh/sshd_config
   # Set PermitRootLogin no
   sudo systemctl restart ssh
   ```

3. **Setup fail2ban**:
   ```bash
   sudo apt install fail2ban
   sudo systemctl enable fail2ban
   ```

## Monitoring and Maintenance

### Health Monitoring
```bash
# Check service status
docker-compose ps

# View logs
docker-compose logs -f web
docker-compose logs -f nginx
docker-compose logs -f db

# Run health check
./monitor.sh
```

### Database Backup
```bash
# Manual backup
docker-compose run --rm backup

# Backups are automatically created daily at 2 AM
# Located in ./backups/ directory
```

### Updates
```bash
# Update application
git pull
docker-compose build
docker-compose up -d

# Update system packages
sudo apt update && sudo apt upgrade -y
```

## Troubleshooting

### Common Issues

1. **Database connection errors**:
   ```bash
   # Check if database is running
   docker-compose ps db
   
   # Check database logs
   docker-compose logs db
   ```

2. **SSL certificate issues**:
   ```bash
   # Renew certificates manually
   sudo certbot renew
   docker-compose restart nginx
   ```

3. **Application not starting**:
   ```bash
   # Check application logs
   docker-compose logs web
   
   # Restart services
   docker-compose restart
   ```

### Performance Optimization

1. **Increase worker processes** (for high traffic):
   Edit Dockerfile and change gunicorn workers:
   ```dockerfile
   CMD ["gunicorn", "--workers", "8", ...]
   ```

2. **Database optimization**:
   ```bash
   # Add to docker-compose.yml under db service
   command: --innodb-buffer-pool-size=1G --max-connections=200
   ```

## Backup and Recovery

### Full System Backup
```bash
# Create backup directory
mkdir -p /backup/cvbiolabs

# Backup database
docker-compose run --rm backup

# Backup application files
tar -czf /backup/cvbiolabs/app-$(date +%Y%m%d).tar.gz \
  --exclude=logs --exclude=__pycache__ .

# Backup uploaded files
tar -czf /backup/cvbiolabs/uploads-$(date +%Y%m%d).tar.gz uploads/
```

### Recovery
```bash
# Restore database
docker-compose exec db mysql -u root -p cvbiolabs < backup_file.sql

# Restore files
tar -xzf app-backup.tar.gz
tar -xzf uploads-backup.tar.gz
```

## Support

For issues or questions:
1. Check the logs: `docker-compose logs -f`
2. Review this documentation
3. Check the application health: `curl http://yourdomain.com/health`

## Production Checklist

- [ ] Domain pointed to server IP
- [ ] SSL certificates installed and working
- [ ] All environment variables configured
- [ ] Database initialized with proper data
- [ ] Email service configured and tested
- [ ] Payment gateway configured (if applicable)
- [ ] Firewall configured
- [ ] Monitoring setup
- [ ] Backup system working
- [ ] Log rotation configured
- [ ] Security headers verified
