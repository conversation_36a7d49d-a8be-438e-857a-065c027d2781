#!/bin/bash

# CVBioLabs Backup Script
# Creates comprehensive backups of database, files, and configuration

set -e  # Exit on any error

# Configuration
BACKUP_DIR="/backup/cvbiolabs"
RETENTION_DAYS=30
DATE=$(date +%Y%m%d_%H%M%S)
PROJECT_NAME="cvbiolabs"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Create backup directory
mkdir -p "$BACKUP_DIR"

echo -e "${BLUE}🏥 CVBioLabs Backup Script${NC}"
echo -e "${BLUE}=========================${NC}"
echo "Backup directory: $BACKUP_DIR"
echo "Date: $DATE"
echo ""

# Load environment variables
if [ -f ".env.production" ]; then
    source .env.production
    print_status "Environment variables loaded"
else
    print_error ".env.production file not found"
    exit 1
fi

# Function to backup database
backup_database() {
    print_info "Starting database backup..."
    
    local backup_file="$BACKUP_DIR/database_${DATE}.sql"
    local compressed_file="$backup_file.gz"
    
    # Create database backup
    if docker-compose exec -T db mysqldump \
        -u root \
        -p"$DB_ROOT_PASSWORD" \
        --single-transaction \
        --routines \
        --triggers \
        --all-databases > "$backup_file"; then
        
        # Compress the backup
        gzip "$backup_file"
        
        local size=$(du -h "$compressed_file" | cut -f1)
        print_status "Database backup completed: $compressed_file ($size)"
        
        return 0
    else
        print_error "Database backup failed"
        return 1
    fi
}

# Function to backup application files
backup_application() {
    print_info "Starting application backup..."
    
    local backup_file="$BACKUP_DIR/application_${DATE}.tar.gz"
    
    # Create application backup excluding unnecessary files
    if tar -czf "$backup_file" \
        --exclude='logs/*' \
        --exclude='__pycache__' \
        --exclude='*.pyc' \
        --exclude='flask_session/*' \
        --exclude='.git' \
        --exclude='backups' \
        --exclude='*.log' \
        .; then
        
        local size=$(du -h "$backup_file" | cut -f1)
        print_status "Application backup completed: $backup_file ($size)"
        
        return 0
    else
        print_error "Application backup failed"
        return 1
    fi
}

# Function to backup uploaded files
backup_uploads() {
    print_info "Starting uploads backup..."
    
    if [ -d "uploads" ] && [ "$(ls -A uploads)" ]; then
        local backup_file="$BACKUP_DIR/uploads_${DATE}.tar.gz"
        
        if tar -czf "$backup_file" uploads/; then
            local size=$(du -h "$backup_file" | cut -f1)
            print_status "Uploads backup completed: $backup_file ($size)"
            return 0
        else
            print_error "Uploads backup failed"
            return 1
        fi
    else
        print_warning "No uploads directory or empty uploads directory"
        return 0
    fi
}

# Function to backup configuration
backup_configuration() {
    print_info "Starting configuration backup..."
    
    local backup_file="$BACKUP_DIR/config_${DATE}.tar.gz"
    
    # Backup configuration files (excluding sensitive data)
    if tar -czf "$backup_file" \
        docker-compose.yml \
        Dockerfile \
        nginx/ \
        gunicorn.conf.py \
        requirements.txt \
        .env.production.template \
        *.md \
        *.sh; then
        
        local size=$(du -h "$backup_file" | cut -f1)
        print_status "Configuration backup completed: $backup_file ($size)"
        
        return 0
    else
        print_error "Configuration backup failed"
        return 1
    fi
}

# Function to backup SSL certificates
backup_ssl() {
    print_info "Starting SSL certificates backup..."
    
    if [ -d "/etc/letsencrypt/live" ]; then
        local backup_file="$BACKUP_DIR/ssl_${DATE}.tar.gz"
        
        if sudo tar -czf "$backup_file" /etc/letsencrypt/; then
            sudo chown $(whoami):$(whoami) "$backup_file"
            local size=$(du -h "$backup_file" | cut -f1)
            print_status "SSL certificates backup completed: $backup_file ($size)"
            return 0
        else
            print_error "SSL certificates backup failed"
            return 1
        fi
    else
        print_warning "No SSL certificates found"
        return 0
    fi
}

# Function to create backup manifest
create_manifest() {
    print_info "Creating backup manifest..."
    
    local manifest_file="$BACKUP_DIR/manifest_${DATE}.txt"
    
    cat > "$manifest_file" << EOF
CVBioLabs Backup Manifest
========================
Date: $(date)
Server: $(hostname)
User: $(whoami)
Backup Directory: $BACKUP_DIR

Files in this backup:
EOF
    
    ls -la "$BACKUP_DIR"/*_${DATE}.* >> "$manifest_file" 2>/dev/null || true
    
    print_status "Backup manifest created: $manifest_file"
}

# Function to clean old backups
cleanup_old_backups() {
    print_info "Cleaning up old backups (older than $RETENTION_DAYS days)..."
    
    local deleted_count=0
    
    # Find and delete old backup files
    if find "$BACKUP_DIR" -name "*.gz" -type f -mtime +$RETENTION_DAYS -delete 2>/dev/null; then
        deleted_count=$(find "$BACKUP_DIR" -name "*.txt" -type f -mtime +$RETENTION_DAYS -delete 2>/dev/null | wc -l)
        find "$BACKUP_DIR" -name "*.txt" -type f -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
    fi
    
    if [ $deleted_count -gt 0 ]; then
        print_status "Cleaned up $deleted_count old backup files"
    else
        print_info "No old backup files to clean up"
    fi
}

# Function to verify backups
verify_backups() {
    print_info "Verifying backup integrity..."
    
    local verification_failed=0
    
    # Check if backup files exist and are not empty
    for file in "$BACKUP_DIR"/*_${DATE}.*; do
        if [ -f "$file" ]; then
            if [ -s "$file" ]; then
                print_status "✓ $(basename "$file") - OK"
            else
                print_error "✗ $(basename "$file") - Empty file"
                verification_failed=1
            fi
        fi
    done
    
    # Test database backup integrity
    local db_backup="$BACKUP_DIR/database_${DATE}.sql.gz"
    if [ -f "$db_backup" ]; then
        if zcat "$db_backup" | head -10 | grep -q "MySQL dump"; then
            print_status "✓ Database backup integrity - OK"
        else
            print_error "✗ Database backup integrity - Failed"
            verification_failed=1
        fi
    fi
    
    return $verification_failed
}

# Function to send backup notification (if configured)
send_notification() {
    local status=$1
    local message=$2
    
    # This is a placeholder for notification system
    # You can integrate with email, Slack, Discord, etc.
    
    if [ "$status" = "success" ]; then
        print_status "Backup completed successfully"
    else
        print_error "Backup completed with errors: $message"
    fi
}

# Main backup process
main() {
    local backup_failed=0
    local error_messages=""
    
    # Perform backups
    if ! backup_database; then
        backup_failed=1
        error_messages="Database backup failed. "
    fi
    
    if ! backup_application; then
        backup_failed=1
        error_messages="${error_messages}Application backup failed. "
    fi
    
    if ! backup_uploads; then
        backup_failed=1
        error_messages="${error_messages}Uploads backup failed. "
    fi
    
    if ! backup_configuration; then
        backup_failed=1
        error_messages="${error_messages}Configuration backup failed. "
    fi
    
    if ! backup_ssl; then
        backup_failed=1
        error_messages="${error_messages}SSL backup failed. "
    fi
    
    # Create manifest
    create_manifest
    
    # Verify backups
    if ! verify_backups; then
        backup_failed=1
        error_messages="${error_messages}Backup verification failed. "
    fi
    
    # Clean up old backups
    cleanup_old_backups
    
    # Show summary
    echo ""
    echo -e "${BLUE}Backup Summary${NC}"
    echo -e "${BLUE}==============${NC}"
    
    if [ $backup_failed -eq 0 ]; then
        print_status "All backups completed successfully!"
        send_notification "success" ""
    else
        print_error "Some backups failed: $error_messages"
        send_notification "error" "$error_messages"
        exit 1
    fi
    
    # Show backup directory contents
    echo ""
    print_info "Backup files created:"
    ls -lah "$BACKUP_DIR"/*_${DATE}.* 2>/dev/null || print_warning "No backup files found"
    
    # Show disk usage
    echo ""
    print_info "Backup directory disk usage:"
    du -sh "$BACKUP_DIR"
}

# Check if running with specific backup type
case "${1:-all}" in
    "database")
        backup_database
        ;;
    "application")
        backup_application
        ;;
    "uploads")
        backup_uploads
        ;;
    "config")
        backup_configuration
        ;;
    "ssl")
        backup_ssl
        ;;
    "all"|*)
        main
        ;;
esac
