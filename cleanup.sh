#!/bin/bash

# CVBioLabs Cleanup Script
# Removes development files, cache, and temporary data before production deployment

echo "🧹 CVBioLabs Cleanup Script"
echo "=========================="

# Remove Python cache files
echo "🗑️  Removing Python cache files..."
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyc" -delete 2>/dev/null || true
find . -name "*.pyo" -delete 2>/dev/null || true

# Remove Flask session files
echo "🗑️  Removing Flask session files..."
rm -rf flask_session/ 2>/dev/null || true

# Remove log files (they'll be recreated)
echo "🗑️  Removing old log files..."
rm -f logs/*.log 2>/dev/null || true
rm -f *.log 2>/dev/null || true

# Remove development/testing files
echo "🗑️  Removing development files..."
rm -f test_*.py 2>/dev/null || true
rm -f *_test.py 2>/dev/null || true

# Remove temporary files
echo "🗑️  Removing temporary files..."
rm -f *.tmp 2>/dev/null || true
rm -f *.bak 2>/dev/null || true
rm -f *.backup 2>/dev/null || true

# Remove IDE files
echo "🗑️  Removing IDE files..."
rm -rf .vscode/ 2>/dev/null || true
rm -rf .idea/ 2>/dev/null || true
rm -f *.swp 2>/dev/null || true
rm -f *.swo 2>/dev/null || true

# Remove OS-specific files
echo "🗑️  Removing OS files..."
rm -f .DS_Store 2>/dev/null || true
rm -f Thumbs.db 2>/dev/null || true

# Remove any .env files except template
echo "🗑️  Removing .env files (keeping templates)..."
rm -f .env 2>/dev/null || true
rm -f .env.local 2>/dev/null || true
rm -f .env.development 2>/dev/null || true

echo "✅ Cleanup completed!"
echo ""
echo "📋 Files preserved:"
echo "• .env.production.template"
echo "• .env.production (if exists)"
echo "• All source code files"
echo "• Static assets"
echo "• Templates"
echo "• Configuration files"
echo ""
echo "🚀 Your project is now clean and ready for production deployment!"
