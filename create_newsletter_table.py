#!/usr/bin/env python3
"""
Script to create the newsletter_subscriptions table
"""

import mysql.connector
from mysql.connector import Error
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def create_newsletter_table():
    """Create the newsletter_subscriptions table"""
    try:
        # Database connection parameters
        connection = mysql.connector.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            database=os.getenv('DB_NAME', 'cvbiolabs'),
            user=os.getenv('DB_USER', 'root'),
            password=os.getenv('DB_PASSWORD', 'root'),
            port=int(os.getenv('DB_PORT', 3306))
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Create newsletter_subscriptions table
            create_table_query = """
            CREATE TABLE IF NOT EXISTS newsletter_subscriptions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                email VARCHAR(255) NOT NULL UNIQUE,
                subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE,
                unsubscribe_token VARCHAR(255) UNIQUE,
                unsubscribed_at TIMESTAMP NULL,
                source VARCHAR(50) DEFAULT 'website',
                ip_address VARCHAR(45),
                user_agent TEXT,
                INDEX idx_email (email),
                INDEX idx_active (is_active),
                INDEX idx_subscribed_at (subscribed_at),
                INDEX idx_unsubscribe_token (unsubscribe_token)
            )
            """
            
            cursor.execute(create_table_query)
            connection.commit()
            
            print("✅ Newsletter subscriptions table created successfully!")
            
            # Verify table creation
            cursor.execute("DESCRIBE newsletter_subscriptions")
            columns = cursor.fetchall()
            
            print("\n📋 Table structure:")
            for column in columns:
                print(f"   {column[0]} - {column[1]}")
                
    except Error as e:
        print(f"❌ Error creating newsletter table: {e}")
        
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            print("\n🔌 Database connection closed.")

if __name__ == "__main__":
    print("🗃️  Creating newsletter subscriptions table...")
    create_newsletter_table()
