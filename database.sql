

CREATE DATABASE IF NOT EXISTS cvbiolabs;
USE cvbiolabs;

-- Admin Users Table
CREATE TABLE admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    professional_id VARCHAR(10) UNIQUE NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('Admin', 'Receptionist', 'Doctor') NOT NULL DEFAULT 'Admin',
    phone VARCHAR(20),
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_professional_id (professional_id),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
);

-- Users Table (Patients)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    status TINYINT(1) NOT NULL DEFAULT 1,
    otp_verified TINYINT(1) NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
);

-- User Profiles Table
CREATE TABLE user_profiles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNIQUE NOT NULL,
    first_name VARCHAR(60),
    last_name VARCHAR(60),
    phone VARCHAR(20),
    date_of_birth DATE,
    gender ENUM('Male', 'Female', 'Other'),
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_phone (phone),
    INDEX idx_name (first_name, last_name)
);

-- Doctors Table
CREATE TABLE doctors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    professional_id VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    specialization VARCHAR(100),
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    licence_number VARCHAR(50) UNIQUE,
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_professional_id (professional_id),
    INDEX idx_email (email),
    INDEX idx_licence (licence_number),
    INDEX idx_specialization (specialization)
);

-- =====================================================
-- TEST AND BOOKING TABLES
-- =====================================================

-- Test Details Table
CREATE TABLE testdetails (
    SrNo INT PRIMARY KEY,
    TestName VARCHAR(255) NOT NULL,
    TestID BIGINT,
    TestCode VARCHAR(255),
    TestAmount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    OutsourceAmount DECIMAL(10,2) DEFAULT 0.00,
    OutsourceCenter VARCHAR(255),
    SampleType VARCHAR(255),
    TestCategory VARCHAR(255),
    DepartmentName VARCHAR(255),
    Accreditation VARCHAR(255),
    IntegrationCode VARCHAR(255),
    ShortText VARCHAR(255),
    CAPTest VARCHAR(5),
    TargetTAT VARCHAR(255),
    VerificationStatus VARCHAR(255),
    TargetTATHHMM VARCHAR(255),
    active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_test_name (TestName),
    INDEX idx_test_code (TestCode),
    INDEX idx_category (TestCategory),
    INDEX idx_active (active)
);

-- Bookings Table
CREATE TABLE bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    test_id INT NOT NULL,
    booking_date DATE NOT NULL,
    appointment_time TIME DEFAULT '00:00:00',
    booking_status ENUM('pending', 'confirmed', 'cancelled', 'completed', 'failed') NOT NULL DEFAULT 'pending',
    barcode VARCHAR(255) UNIQUE NOT NULL,
    address_line1 VARCHAR(255) NOT NULL,
    address_line2 VARCHAR(255),
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    postal_code VARCHAR(20) NOT NULL,
    country VARCHAR(100) NOT NULL DEFAULT 'India',
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (test_id) REFERENCES testdetails(SrNo) ON DELETE RESTRICT,
    INDEX idx_user_id (user_id),
    INDEX idx_test_id (test_id),
    INDEX idx_booking_date (booking_date),
    INDEX idx_status (booking_status),
    INDEX idx_barcode (barcode)
);

-- =====================================================
-- PICKUP AND LOGISTICS TABLES
-- =====================================================

-- Pickup Agents Table
CREATE TABLE pickup_agents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    professional_id VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(15) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    status ENUM('Available', 'Busy', 'Inactive') NOT NULL DEFAULT 'Available',
    vehicle_number VARCHAR(20),
    service_area VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_professional_id (professional_id),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_service_area (service_area)
);

-- Sample Collections Table
CREATE TABLE sample_collections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    agent_id INT NOT NULL,
    collection_status ENUM('Pending', 'Collected', 'Delivered') NOT NULL DEFAULT 'Pending',
    collection_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES pickup_agents(id) ON DELETE RESTRICT,
    INDEX idx_booking_id (booking_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_status (collection_status),
    INDEX idx_collection_date (collection_date)
);

-- =====================================================
-- PAYMENT AND COUPON TABLES
-- =====================================================

-- Payments Table
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('RazorPay', 'UPI', 'Card', 'Net Banking', 'Cash') NOT NULL,
    transaction_id VARCHAR(100) UNIQUE,
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') NOT NULL DEFAULT 'pending',
    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    refund_amount DECIMAL(10,2) DEFAULT 0.00,
    refund_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    INDEX idx_booking_id (booking_id),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_payment_status (payment_status),
    INDEX idx_payment_date (payment_date)
);

-- Coupons Table
CREATE TABLE coupons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    discount_amount DECIMAL(10,2) NOT NULL,
    expiry_date DATE NOT NULL,
    status ENUM('Active', 'Expired', 'Used') NOT NULL DEFAULT 'Active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_code (code),
    INDEX idx_status (status),
    INDEX idx_expiry (expiry_date)
);

-- Coupon Usage Table
CREATE TABLE coupon_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    coupon_id INT NOT NULL,
    user_id INT NOT NULL,
    booking_id INT,
    used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE SET NULL,
    UNIQUE KEY unique_coupon_user (coupon_id, user_id),
    INDEX idx_coupon_id (coupon_id),
    INDEX idx_user_id (user_id),
    INDEX idx_booking_id (booking_id)
);

-- =====================================================
-- REPORTS AND MEDICAL TABLES
-- =====================================================

-- Reports Table
CREATE TABLE reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    patient_id INT NOT NULL,
    test_id INT NOT NULL,
    doctor_id INT,
    assigned_doctor_id INT,
    assign_status ENUM('Not Assigned', 'Assigned') NOT NULL DEFAULT 'Not Assigned',
    report_url VARCHAR(255) NOT NULL,
    report_status ENUM('Pending', 'Verified', 'Completed') NOT NULL DEFAULT 'Pending',
    doctor_review TEXT,
    comments TEXT,
    report_type ENUM('standard', 'detailed', 'summary') NOT NULL DEFAULT 'standard',
    report_format ENUM('pdf', 'excel', 'csv') NOT NULL DEFAULT 'pdf',
    file_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (patient_id) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (test_id) REFERENCES testdetails(SrNo) ON DELETE RESTRICT,
    FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE SET NULL,
    FOREIGN KEY (assigned_doctor_id) REFERENCES doctors(id) ON DELETE SET NULL,
    INDEX idx_booking_id (booking_id),
    INDEX idx_patient_id (patient_id),
    INDEX idx_test_id (test_id),
    INDEX idx_doctor_id (doctor_id),
    INDEX idx_assigned_doctor (assigned_doctor_id),
    INDEX idx_report_status (report_status)
);

-- Patient Report Table
CREATE TABLE patient_report (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id INT NOT NULL,
    booking_id INT NOT NULL,
    patient_id INT NOT NULL,
    test_name VARCHAR(255) NOT NULL,
    barcode VARCHAR(255) NOT NULL,
    report_url VARCHAR(255) NOT NULL,
    report_status ENUM('Pending', 'Verified', 'Completed') NOT NULL DEFAULT 'Pending',
    comment TEXT,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sent_by_receptionist_id INT,
    verified_by_admin_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (report_id) REFERENCES reports(id) ON DELETE CASCADE,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (patient_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (sent_by_receptionist_id) REFERENCES admin_users(id) ON DELETE SET NULL,
    FOREIGN KEY (verified_by_admin_id) REFERENCES admin_users(id) ON DELETE SET NULL,
    INDEX idx_report_id (report_id),
    INDEX idx_booking_id (booking_id),
    INDEX idx_patient_id (patient_id),
    INDEX idx_barcode (barcode),
    INDEX idx_status (report_status)
);

-- Report History Table
CREATE TABLE report_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id INT NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'Pending',
    action VARCHAR(50) NOT NULL DEFAULT 'Created',
    changed_by INT,
    doctor_id INT,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    comments TEXT,
    details TEXT,
    FOREIGN KEY (report_id) REFERENCES reports(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES admin_users(id) ON DELETE SET NULL,
    FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE SET NULL,
    INDEX idx_report_id (report_id),
    INDEX idx_changed_by (changed_by),
    INDEX idx_doctor_id (doctor_id),
    INDEX idx_changed_at (changed_at)
);

-- =====================================================
-- UTILITY AND SYSTEM TABLES
-- =====================================================

-- Referrals Table
CREATE TABLE referrals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    referrer_id INT NOT NULL,
    referred_id INT NOT NULL,
    referral_code VARCHAR(50) NOT NULL,
    referral_status ENUM('Pending', 'Rewarded') NOT NULL DEFAULT 'Pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_referrer (referrer_id),
    INDEX idx_referred (referred_id),
    INDEX idx_code (referral_code),
    INDEX idx_status (referral_status)
);

-- OTP Verification Table
CREATE TABLE otp_verification (
    id INT AUTO_INCREMENT PRIMARY KEY,
    phone VARCHAR(15) NOT NULL,
    email VARCHAR(100) NOT NULL,
    otp VARCHAR(6) NOT NULL,
    verified TINYINT(1) NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    INDEX idx_phone (phone),
    INDEX idx_email (email),
    INDEX idx_expires (expires_at)
);

-- Session Logs Table
CREATE TABLE session_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_event_type (event_type),
    INDEX idx_created_at (created_at)
);

-- Audit Logs Table
CREATE TABLE audit_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    username VARCHAR(255),
    action VARCHAR(255) NOT NULL,
    details TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_timestamp (timestamp)
);


-- =====================================================
-- INITIAL DATA AND SETUP
-- =====================================================

use cvbiolabs;
INSERT INTO TestDetails (SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM) VALUES (379, 'Allergy Drugs', 7780833, 'Allergy Drugs', 1760, 0, 'SELF', 'Serum', 'Routine Outsource TT3', 'Allergy', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'), (107, 'Stone Analysis', 7781338, 'Stone Analysis', 2090, 0, 'SELF', 'Calculus', '-', 'Biochemistry', 'Not enable', 'BHK001', '-', 'NO', '-', 'Not Verified', '-'), (188, 'Globulin', 7781053, 'Globulin', 110, 0, 'SELF', 'Serum', '-', 'Biochemistry', 'Not enable', 'Same Day', 'Glob', 'NO', '840Minutes', 'Not Verified', '14:0'), (196, 'Creatinine', 7781045, 'Creatinine', 220, 0, 'SELF', 'Serum', '-', 'Biochemistry', 'Not enable', 'Same Day', 'CRE', 'NO', '840Minutes', 'Not Verified', '14:0'), (199, 'Porphobilinogen (Quanlitative)', 7781042, 'Porphobilinogen (Quanlitative)', 550, 0, 'SELF', '24Hr Urine', '-', 'Biochemistry', 'Not enable', '4th Working Day', 'PBG, Urine', 'NO', '840Minutes', 'Not Verified', '14:0');


-- Insert additional data into the TestDetails table
INSERT INTO TestDetails 
(SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM)
VALUES
(214, 'Magnesium (Urine)', 7781025, 'Magnesium (Urine)', 550, 0, 'SELF', 'Serum', '-', 'Biochemistry', 'Not enable', 'Same Day', 'MG', 'NO', '840Minutes', 'Not Verified', '14:0'),
(221, 'Iron Binding Capacity (TIBC)', 7781018, 'Iron Binding Capacity (TIBC)', 440, 0, 'SELF', 'Serum', '-', 'Biochemistry', 'Not enable', 'Same Day', 'TIBC', 'NO', '840Minutes', 'Not Verified', '14:0'),
(232, 'Urinary Cortisol/Creatinine Ratio', 7781006, 'Urinary Cortisol/Creatinine Ratio', 660, 0, 'SELF', '24Hr Urine', '-', 'Biochemistry', 'Not enable', 'Same Day', 'Cort/Cre Ratio', 'NO', '840Minutes', 'Not Verified', '14:0'),
(233, 'Iron Pro le (Iron,TIBC,Tranferrin,% Saturation)', 7781005, 'Iron Pro le (Iron,TIBC,Tranferrin,% Saturation)', 660, 0, 'SELF', 'Serum', '-', 'Biochemistry', 'Not enable', 'Same Day', 'IronProfile-Adv.', 'NO', '840Minutes', 'Not Verified', '14:0'),
(251, 'Protein Total', 7780986, 'Protein Total', 110, 0, 'SELF', 'Serum', '-', 'Biochemistry', 'Not enable', 'Same Day', 'TP', 'NO', '840Minutes', 'Not Verified', '14:0'),
(258, 'Ionized Calicum', 7780979, 'Ionized Calicum', 550, 0, 'SELF', 'Serum', '-', 'Biochemistry', 'Not enable', 'Same Day', 'Ca-Ion', 'NO', '840Minutes', 'Not Verified', '14:0'),
(30, 'RENAL PROFILE/KIDNEY FUNCTION TEST (RFT/KFT)', 7876911, 'RFT/KFT', 660, 0, 'SELF', 'Serum', '-', 'Biochemistry', 'Not enable', 'Same Day', '-', 'NO', '840Minutes', 'Not Verified', '14:0'),
(10, 'Serum. Beta HCG', 9665376, 'Beta Human Chorionic Gonodotropin Hormone', 700, 700, 'SELF', 'Fluid', '-', 'Clinical Biochemistry', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(103, 'Glucose - PLBS', 7781387, 'Glucose - PLBS', 55, 0, 'SELF', 'Plasma - P', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHG029', 'PPBS', 'NO', '-', 'Not Verified', '-'),
(104, 'Beta Human Chorionic Gonodotropin Hormone Titre', 7781367, 'Beta Human Chorionic Gonodotropin Hormone Titre', 660, 0, 'SELF', 'Serum', 'Specialise InHouse TT4', 'Clinical Biochemistry', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-');


-- Insert additional data into the TestDetails table
INSERT INTO TestDetails 
(SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM)
VALUES
(126, 'Dengue (total Screen)', 7781248, 'Dengue (total Screen)', 2750, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BH037G', 'DENGUE PROFILE', 'NO', '-', 'Not Verified', '-'),
(130, 'Drugs panel (5) opiate, marijana, Benzodiazepine, cocaine Amphetamine', 7781234, 'Drugs panel (5) opiate, marijana, Benzodiazepine, cocaine Amphetamine', 2200, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(131, 'Complement 3 C3', 7781233, 'Complement 3 C3', 1045, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Clinical Biochemistry', 'Not enable', 'BHC005', 'C3 COM', 'NO', '-', 'Not Verified', '-'),
(16, 'Glucose - 2hr', 9470133, 'Glucose - 2hr', 55, 0, 'SELF', 'Plasma - R', 'Clinical Biochemistry', 'Clinical Biochemistry', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(17, 'Glucose - 1hr', 9470131, 'Glucose - 1hr', 55, 0, 'SELF', 'Plasma - P', 'Clinical Biochemistry', 'Clinical Biochemistry', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(282, 'Troponin - I', 7780932, 'Troponin - I', 1375, 0, 'SELF', 'Serum', 'Specialise InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHT027', 'TROP - I(Quali)', 'NO', '-', 'Not Verified', '-'),
(283, 'Protein/Creatinine Ratio', 7780931, 'Protein/Creatinine Ratio', 385, 0, 'SELF', 'Urine', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHP054', 'PRO/CRE', 'NO', '-', 'Not Verified', '-'),
(285, 'Apolipoprotein A1 (APO-A1)', 7780929, 'Apolipoprotein A1 (APO-A1)', 550, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Clinical Biochemistry', 'Not enable', 'IA0467', 'APOLIPO-A1', 'NO', '-', 'Not Verified', '-'),
(286, 'CYFRA 21 - 1 Lung Cancer Marker', 7780928, 'CYFRA 21 - 1 Lung Cancer Marker', 4400, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHC122', '-', 'NO', '-', 'Not Verified', '-'),
(287, 'C-Peptide', 7780927, 'C-Peptide', 1265, 0, 'SELF', 'Serum', 'Specialise InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHC105', 'C-PEPTIDE', 'NO', '-', 'Not Verified', '-');


-- Insert additional data into the TestDetails table
INSERT INTO TestDetails 
(SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM)
VALUES
(290, 'Urinary Calculi', 7780924, 'Urinary Calculi', 330, 0, 'SELF', 'Urine', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHC029', '-', 'NO', '-', 'Not Verified', '-'),
(292, '1,25 Hydroxy Vitamin D (Total)', 7780922, '1,25 Hydroxy Vitamin D (Total)', 1980, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHV016', 'D-25', 'NO', '-', 'Not Verified', '-'),
(293, 'Creatinine Kinase (CPK)', 7780921, 'Creatinine Kinase (CPK)', 330, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHC107', 'CPK', 'NO', '-', 'Not Verified', '-'),
(294, 'Estriol,Unconjugated (E3)', 7780920, 'Estriol,Unconjugated (E3)', 1320, 0, 'SELF', 'Serum', 'Specialise InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHE017', 'UE3', 'NO', '-', 'Not Verified', '-'),
(295, 'Aspartate Aminotransferase (AST/SGOT)', 7780919, 'Aspartate Aminotransferase (AST/SGOT)', 154, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHS009V', 'SGOT', 'NO', '-', 'Not Verified', '-'),
(296, 'Osmolality - Urine', 7780918, 'Osmolality - Urine', 770, 0, 'SELF', 'Urine', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHO006', 'OSM', 'NO', '-', 'Not Verified', '-'),
(297, 'Estrogen/Progesterone Receptor (ER/PR)', 7780917, 'Estrogen/Progesterone Receptor (ER/PR)', 2750, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHP047', 'PROG', 'NO', '-', 'Not Verified', '-'),
(298, 'Immuno xation Electrophoresis', 7780916, 'Immuno xation Electrophoresis', 6600, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(299, 'Digoxin', 7780915, 'Digoxin', 1320, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHD009', '-', 'NO', '-', 'Not Verified', '-'),
(302, 'Angiotensin Converting Enzyme (ACE)', 7780912, 'Angiotensin Converting Enzyme (ACE)', 1320, 0, 'SELF', 'Serum', 'Routine Outsource TT1', 'Clinical Biochemistry', 'Not enable', 'BHA074', 'ACE', 'NO', '-', 'Not Verified', '-');

-- Insert additional data into the TestDetails table
INSERT INTO TestDetails 
(SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM)
VALUES
(303, 'Benzodiazepines (Serum)', 7780911, 'Benzodiazepines (Serum)', 1320, 0, 'SELF', 'Serum', 'Routine Outsource TT3', 'Clinical Biochemistry', 'Not enable', '-', '-', 'NO', '480Minutes', 'Not Verified', '8:0'),
(304, 'Anti Thrombin III (Antigenic)', 7780910, 'Anti Thrombin III (Antigenic)', 3850, 0, 'SELF', 'PLASMA', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(314, 'Estradiol (E2)', 7780900, 'Estradiol (E2)', 495, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHE016', 'E2', 'NO', '-', 'Not Verified', '-'),
(317, 'Thyroxine - Free (FT4)', 7780897, 'Thyroxine - Free (FT4)', 550, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BH146G', 'FT4', 'NO', '-', 'Not Verified', '-'),
(321, 'Gastrin', 7780893, 'Gastrin', 1650, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHG015', '-', 'NO', '-', 'Not Verified', '-'),
(323, 'Glucose 6 Phosphate Dehydrogenase (G6PD quantitative)', 7780891, 'Glucose 6 Phosphate Dehydrogenase (G6PD quantitative)', 660, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHG039', 'G6PD', 'NO', '-', 'Not Verified', '-'),
(331, 'Carbamazepine(Tegretol)', 7780883, 'Carbamazepine(Tegretol)', 660, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHC034', '-', 'NO', '-', 'Not Verified', '-'),
(339, 'Marijuana', 7780874, 'Marijuana', 660, 0, 'SELF', 'Urine', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHC041n', '-', 'NO', '-', 'Not Verified', '-'),
(342, 'Rheumatoid Factor (RA Test)', 7780871, 'Rheumatoid Factor (RA Test)', 440, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHR004', 'RA Quali', 'NO', '-', 'Not Verified', '-'),
(343, 'Anti Thyroglobulin Antibody (ATG)', 7780870, 'Anti Thyroglobulin Antibody (ATG)', 660, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHA064', 'ANTI TG', 'NO', '-', 'Not Verified', '-');


INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) 
VALUES
(344, 'Intact Parathyroid Hormone (IPTH)', 7780869, 'BHP066', 935, 0, 'SELF', 'Serum', 'Specialise InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHP066', 'PTH', 'NO', '-', 'Not Verified', NULL),
(347, 'Amylase', 7780866, 'BHA040', 330, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHA040', 'AMY-Serum', 'NO', '-', 'Not Verified', NULL),
(350, 'Insulin - Fasting', 7780863, 'BHI013', 715, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHI013', 'INSULIN F', 'NO', '-', 'Not Verified', NULL),
(352, 'Serum Light chains', 7780861, NULL, 8800, 0, 'SELF', 'Urine', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(376, 'Lactate', 7780836, 'BHL002', 935, 0, 'SELF', 'Fluoride Plasma', 'Specialise InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHL002', 'LACTATE', 'NO', '-', 'Not Verified', NULL),
(377, 'Troponin - T', 7780835, 'BHT028', 1980, 0, 'SELF', 'Serum', 'Specialise InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHT028', 'TROP - T(Quali)', 'NO', '-', 'Not Verified', NULL),
(378, 'Procalcitonin', 7780834, 'BHP046', 2640, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Clinical Biochemistry', 'Not enable', 'BHP046', 'PCT', 'NO', '-', 'Not Verified', NULL),
(380, 'Glomerular Filtration Rate (GFR)', 7780832, 'BHG038', 825, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHG038', 'GFR & CRE', 'NO', '-', 'Not Verified', NULL),
(381, 'Alcohol', 7780831, 'BHA014', 1320, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHA014', 'ALCH', 'NO', '-', 'Not Verified', NULL),
(382, 'Barbiturates', 7780830, 'BHB001', 2310, 0, 'SELF', 'Urine', 'Routine Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHB001', NULL, 'NO', '-', 'Not Verified', NULL),
(383, 'Alanine Transaminase(ALT/SGPT)', 7780829, 'BHS010', 200, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHS010', 'SGPT', 'NO', '-', 'Not Verified', NULL),
(384, 'Uric Acid', 7780828, 'BHU011', 165, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHU011', 'URIC', 'NO', '-', 'Not Verified', NULL),
(385, 'Cadmium', 7780827, 'BHC018', 2640, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHC018', NULL, 'NO', '-', 'Not Verified', NULL),
(386, 'Aluminium', 7780826, 'BHA030', 2750, 0, 'SELF', 'Serum', 'Specialise Outsource TT4', 'Clinical Biochemistry', 'Not enable', 'BHA030', NULL, 'NO', '-', 'Not Verified', NULL),
(387, 'Chloride', 7780825, 'BHC049', 165, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHC049', 'CL', 'NO', '-', 'Not Verified', NULL);


INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) 
VALUES
(388, 'Cholesterol - Total', 7780824, 'Cholesterol - Total', 220, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHC053', 'CHOL', 'NO', '-', 'Not Verified', NULL),
(389, 'Myoglobulin', 7780823, 'Myoglobulin', 2255, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHM046', NULL, 'NO', '-', 'Not Verified', NULL),
(390, 'Serum LDH', 7780822, 'Serum LDH', 550, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHL005', 'LDH', 'NO', '-', 'Not Verified', NULL),
(391, 'Alkaline phosphatase (ALP)', 7780820, 'Alkaline phosphatase (ALP)', 110, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHA022V', 'ALP', 'NO', '-', 'Not Verified', NULL),
(392, 'Creatinine Clearance Test', 7780819, 'Creatinine Clearance Test', 550, 0, 'SELF', 'Urine', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHC111', 'CRE CLEARANCE TEST', 'NO', '-', 'Not Verified', NULL),
(393, 'Luteinising Hormone (LH)', 7780818, 'Luteinising Hormone (LH)', 500, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHL016', 'LH', 'NO', '-', 'Not Verified', NULL),
(394, 'C- Reactive Protein (CRP)', 7780817, 'C- Reactive Protein (CRP)', 440, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'DNT673299', 'CRP', 'NO', '-', 'Not Verified', NULL),
(395, 'Growth Hormone (HGH)', 7780816, 'Growth Hormone (HGH)', 715, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHG037', NULL, 'NO', '-', 'Not Verified', NULL),
(398, 'Magnesium', 7780813, 'Magnesium', 385, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHM004', 'MG', 'NO', '-', 'Not Verified', NULL),
(399, 'Valproic Acid (Sodium Valproate)', 7780812, 'Valproic Acid (Sodium Valproate)', 440, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHV001', NULL, 'NO', '-', 'Not Verified', NULL),
(400, 'Sex Hormone Binding Globulin (SHBG)', 7780811, 'Sex Hormone Binding Globulin (SHBG)', 880, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHE018', NULL, 'NO', '-', 'Not Verified', NULL),
(401, 'Serum - Ascites Albumin Gradient (SAAG)', 7780810, 'Serum - Ascites Albumin Gradient (SAAG)', 220, 0, 'SELF', 'Serum', 'Specialise Outsource TT4', 'Clinical Biochemistry', 'Not enable', NULL, 'SAAG', 'NO', '-', 'Not Verified', NULL),
(402, 'Thyroid Stimulating Hormone (TSH)', 7780809, 'Thyroid Stimulating Hormone (TSH)', 275, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHT029', 'TSH', 'NO', '-', 'Not Verified', NULL),
(403, 'D-Dimer', 7780808, 'D-Dimer', 1320, 0, 'SELF', 'PLASMA', 'Specialise InHouse TT2', 'Clinical Biochemistry', 'Not enable', 'BHD001', 'D-DIMER', 'NO', '-', 'Not Verified', NULL),
(404, 'Beta Human Chorionic Gonodotropin Hormone', 7780807, 'Beta Human Chorionic Gonodotropin Hormone', 550, 0, 'SELF', 'Serum', 'Routine InHouse TT2', 'Clinical Biochemistry', 'Not enable', 'BHH049', 'HCG BETA', 'NO', '-', 'Not Verified', NULL);

INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) 
VALUES
(405, 'Cortisol Serum (7 to 9 pm)', 7780805, 'Cortisol Serum (7 to 9 pm)', 700, 0, 'SELF', 'Serum', 'Specialise InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHC093', 'CORTISOL-EVENING', 'NO', '-', 'Not Verified', NULL),
(406, 'Ammonia', 7780804, 'Ammonia', 880, 0, 'SELF', 'Serum', 'Special (S)', 'Clinical Biochemistry', 'Not enable', 'BHA034', NULL, 'NO', '-', 'Not Verified', NULL),
(407, 'Protein - Fluids', 7780803, 'Protein - Fluids', 110, 0, 'SELF', 'Fluid', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHP060', 'PRO', 'NO', '-', 'Not Verified', NULL),
(408, 'Alpha-1 Antitrypsin (AAT)', 7780802, 'Alpha-1 Antitrypsin (AAT)', 2420, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHA026', NULL, 'NO', '-', 'Not Verified', NULL),
(410, 'Insulin-Like Growth Factor (IGF - I)', 7780800, 'Insulin-Like Growth Factor (IGF - I)', 3300, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHI022', NULL, 'NO', '-', 'Not Verified', NULL),
(414, 'Cholesterol -LDL Only', 7780796, 'Cholesterol -LDL Only', 275, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHC052', 'CHOL', 'NO', '-', 'Not Verified', NULL),
(415, 'Cholinesterase', 7780795, 'Cholinesterase', 880, 0, 'SELF', 'Serum', 'Specialise InHouse TT3', 'Clinical Biochemistry', 'Not enable', 'BHC054', NULL, 'NO', '-', 'Not Verified', NULL),
(420, 'Potassium', 7780790, 'Potassium', 220, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHP041', 'K', 'NO', '-', 'Not Verified', NULL),
(421, 'B-Type Natriuric Peptide (BNP)', 7780789, 'B-Type Natriuric Peptide (BNP)', 2750, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Clinical Biochemistry', 'Not enable', 'BHN010', 'NT-PROBNP', 'NO', '-', 'Not Verified', NULL),
(422, 'Oxalates', 7780788, 'Oxalates', 1980, 0, 'SELF', '24Hr Urine', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHO010', NULL, 'NO', '-', 'Not Verified', NULL),
(423, 'Protein - 24 hrs Urine', 7780787, 'Protein - 24 hrs Urine', 110, 0, 'SELF', '24Hr Urine', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHP059', '24HRS UR PRT', 'NO', '-', 'Not Verified', NULL),
(424, 'Protein Electrophoresis', 7780786, 'Protein Electrophoresis', 825, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHP056', NULL, 'NO', '-', 'Not Verified', NULL),
(429, 'Osmolality - Serum', 7780781, 'Osmolality - Serum', 770, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHO005', 'OSM', 'NO', '-', 'Not Verified', NULL),
(433, 'Calcium', 7780777, 'Calcium', 220, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHC032', 'CAL', 'NO', '-', 'Not Verified', NULL),
(434, 'Amphetamine', 7780776, 'Amphetamine', 770, 0, 'SELF', 'Urine', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHA036', NULL, 'NO', '-', 'Not Verified', NULL),
(435, 'Iron', 7780775, 'Iron', 385, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BH102G', 'IRON', 'NO', '-', 'Not Verified', NULL),
(437, 'Opiates', 7780773, 'Opiates', 660, 0, 'SELF', 'Urine', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHO001', NULL, 'NO', '-', 'Not Verified', NULL),
(438, 'Protein Electrophoresis (Urine)', 7780772, 'Protein Electrophoresis (Urine)', 825, 0, 'SELF', '24Hr Urine', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHP055', NULL, 'NO', '-', 'Not Verified', NULL),
(439, 'RBC Folate', 7780771, 'RBC Folate', 1705, 0, 'SELF', 'Serum', 'Specialise InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHF062', 'FOLIC', 'NO', '-', 'Not Verified', NULL),
(442, 'Glucose - Fasting', 7780768, 'Glucose - Fasting', 55, 0, 'SELF', 'Plasma - F', 'Clinical Biochemistry', 'Clinical Biochemistry', 'Not enable', 'BHG024', 'FBS', 'NO', '-', 'Not Verified', NULL),
(443, 'Albumin', 7780767, 'Albumin', 110, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHA013', 'ALB', 'NO', '-', 'Not Verified', NULL),
(445, 'Copper', 7780765, 'Copper', 880, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHC074', NULL, 'NO', '-', 'Not Verified', NULL),
(446, 'Creatine Kinase MB (CK -MB)', 7780764, 'Creatine Kinase MB (CK -MB)', 440, 0, 'SELF', 'Serum', 'Routine InHouse TT2', 'Clinical Biochemistry', 'Not enable', 'BHC108', 'CPK-MB', 'NO', '-', 'Not Verified', NULL),
(447, 'Anti microsomal thyroperoxidase antibody(AMA)', 7780763, 'Anti microsomal thyroperoxidase antibody(AMA)', 660, 0, 'SELF', 'Serum', NULL, 'Clinical Biochemistry', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL);

INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) 
VALUES
(448, 'BUN/Creatinine Ratio', 7780762, 'BUN/Creatinine Ratio', 220, 0, 'SELF', 'Serum', '-', 'Clinical Biochemistry', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(449, 'Zinc', 7780761, 'Zinc', 2200, 0, 'SELF', 'Serum', 'Specialise Outsource TT4', 'Clinical Biochemistry', 'Not enable', 'BHZ003', '-', 'NO', '-', 'Not Verified', NULL),
(455, 'Negative Stain (Indian Ink)', 7780754, 'Negative Stain (Indian Ink)', 275, 0, 'SELF', 'CSF', 'Specialise InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'MCI001', '-', 'NO', '-', 'Not Verified', NULL),
(456, 'Gamma Glutamyl Transferase (GGT)', 7780753, 'Gamma Glutamyl Transferase (GGT)', 385, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHG018', 'GGTP GGT', 'NO', '-', 'Not Verified', NULL),
(457, 'Urine for Chyle', 7780752, 'Urine for Chyle', 330, 0, 'SELF', 'Urine', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(461, 'Triiodothyronine Free (FT3)', 7780748, 'Triiodothyronine Free (FT3)', 550, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(462, 'Apolipoprotein B (APO-B)', 7780747, 'Apolipoprotein B (APO-B)', 550, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Clinical Biochemistry', 'Not enable', 'IA0469', 'APOLIPO-B', 'NO', '-', 'Not Verified', NULL),
(469, 'Phosphorus', 7780740, 'Phosphorus', 165, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHP025', 'PHOS', 'NO', '-', 'Not Verified', NULL),
(472, 'Blood Urea Nitrogen (BUN)', 7780737, 'Blood Urea Nitrogen (BUN)', 165, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHB025', 'BUN', 'NO', '-', 'Not Verified', NULL),
(475, 'Glycosylated Hemoglobin (GHb/HBA1c)', 7780734, 'Glycosylated Hemoglobin (GHb/HBA1c)', 550, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHH003', 'HBA1C', 'NO', '-', 'Not Verified', NULL),
(476, 'PAPP-A', 7780733, 'PAPP-A', 1320, 0, 'SELF', 'Serum', 'Specialise InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHP005', 'PAPPA A', 'NO', '-', 'Not Verified', NULL),
(477, 'Lithium(Li)', 7780732, 'Lithium(Li)', 990, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHL020', '-', 'NO', '-', 'Not Verified', NULL),
(479, 'Cholesterol -HDL', 7780730, 'Cholesterol -HDL', 220, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHC051', 'CHOL-HDL', 'NO', '-', 'Not Verified', NULL),
(480, 'Phenytoin (Eptoin)', 7780729, 'Phenytoin (Eptoin)', 880, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHP017', '-', 'NO', '-', 'Not Verified', NULL),
(482, 'High Sensitive CRP (hs CRP)', 7780727, 'High Sensitive CRP (hs CRP)', 495, 0, 'SELF', 'Serum', 'Specialise InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHC109', 'HS CRP', 'NO', '-', 'Not Verified', NULL),
(483, 'Protein Total with Albumin', 7780726, 'Protein Total with Albumin', 220, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BH136G', 'PRO,ALB,A/G', 'NO', '-', 'Not Verified', NULL),
(495, 'Cocaine', 7780714, 'Cocaine', 770, 0, 'SELF', 'Urine', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHC067', '-', 'NO', '-', 'Not Verified', NULL),
(497, 'Lead', 7780712, 'Lead', 2420, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHL008', '-', 'NO', '-', 'Not Verified', NULL),
(498, 'Benzodiazepines', 7780711, 'Benzodiazepines', 1100, 0, 'SELF', 'Urine', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHB003', '-', 'NO', '-', 'Not Verified', NULL),
(499, 'Thyroglobulin', 7780710, 'Thyroglobulin', 1210, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHT015', 'THYROG,TG', 'NO', '-', 'Not Verified', NULL),
(501, 'Mercury', 7780708, 'Mercury', 3850, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', '-', 'Mercury Blood', 'NO', '-', 'Not Verified', NULL),
(502, 'Porphobilinogen (PBG)', 7780707, 'Porphobilinogen (PBG)', 3300, 0, 'SELF', '24Hr Urine', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(504, 'CA 19.9 - Pancreatic Cancer Marker', 7780705, 'CA 19.9 - Pancreatic Cancer Marker', 880, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Clinical Biochemistry', 'Not enable', 'BHC014', 'CA 19.9', 'NO', '-', 'Not Verified', NULL),
(505, 'Sodium', 7780704, 'Sodium', 165, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHS020', 'NA', 'NO', '-', 'Not Verified', NULL),
(506, 'Lipoprotein a (Lp-a)', 7780703, 'Lipoprotein a (Lp-a)', 935, 0, 'SELF', 'Serum', 'Routine InHouse TT2', 'Clinical Biochemistry', 'Not enable', 'ULP87', 'LP(a)', 'NO', '-', 'Not Verified', NULL),
(507, 'Calcitonin', 7780702, 'Calcitonin', 1430, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHC022', '-', 'NO', '-', 'Not Verified', NULL),
(509, 'Total IgE', 7780700, 'Total IgE', 800, 0, 'SELF', 'Serum', 'Routine (R)', 'Clinical Biochemistry', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(512, 'Triglycerides (TGL)', 7780697, 'Triglycerides (TGL)', 165, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHT026', 'TRIG', 'NO', '-', 'Not Verified', NULL),
(513, 'Adenosine Deaminase (ADA)', 7780696, 'Adenosine Deaminase (ADA)', 440, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', '-', 'ADA', 'NO', '-', 'Not Verified', NULL),
(514, 'Catecholamines', 7780695, 'Catecholamines', 4400, 0, 'SELF', '24Hr Urine', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHU001', '-', 'NO', '-', 'Not Verified', NULL);

INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) 
VALUES
(515, 'Chromium', 7780694, 'Chromium', 3630, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHC056', '-', 'NO', '-', 'Not Verified', NULL),
(519, 'Phenobarbital (Cardinal)', 7780690, 'Phenobarbital (Cardinal)', 880, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHP016', '-', 'NO', '-', 'Not Verified', NULL),
(521, 'Anti Diuretic Harmone (ADH)', 7780688, 'Anti Diuretic Harmone (ADH)', 4400, 0, 'SELF', 'Serum', 'Routine (R)', 'Clinical Biochemistry', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(522, 'Ferritin', 7780687, 'Ferritin', 770, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHF013', 'FER', 'NO', '-', 'Not Verified', NULL),
(524, 'Bicarbonate (Calculated from CO2)', 7780685, 'Bicarbonate (Calculated from CO2)', 550, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHB010', 'BICARBONATE', 'NO', '-', 'Not Verified', NULL),
(529, 'Leptospira Detection', 7780680, 'Leptospira Detection', 440, 0, 'SELF', 'Urine', 'Specialise InHouse TT3', 'Clinical Biochemistry', 'Not enable', 'MCL003', '-', 'NO', '-', 'Not Verified', NULL),
(533, 'VMA (Vanillyl Mandelic Acid)', 7780675, 'VMA (Vanillyl Mandelic Acid)', 3850, 0, 'SELF', '24Hr Urine', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHV020', '-', 'NO', '-', 'Not Verified', NULL),
(535, 'Progesterone', 7780672, 'Progesterone', 550, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHP047', 'PROG', 'NO', '-', 'Not Verified', NULL),
(537, 'Microalbuminuria', 7780670, 'Microalbuminuria', 500, 500, 'SELF', '24Hr Urine', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHM032', '24 HR MICRO', 'NO', '-', 'Not Verified', NULL),
(539, 'Cortisol Urine Total', 7780668, 'Cortisol Urine Total', 1650, 0, 'SELF', '24Hr Urine', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHC090', '-', 'NO', '-', 'Not Verified', NULL),
(542, 'Billirubin Direct', 7780665, 'Billirubin Direct', 110, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHB011', 'DBIL', 'NO', '-', 'Not Verified', NULL),
(544, 'Acid phosphatase-Total', 7780663, 'Acid phosphatase-Total', 660, 0, 'SELF', 'Serum', 'Routine Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHA006', '-', 'NO', '-', 'Not Verified', NULL),
(545, 'CD4 Absolute Count', 7780662, 'CD4 Absolute Count', 990, 0, 'SELF', 'Whole blood', 'Specialise InHouse TT2', 'Clinical Biochemistry', 'Not enable', 'BHC155', 'CD4', 'NO', '-', 'Not Verified', NULL),
(546, 'Androstenedione (A4)', 7780661, 'Androstenedione (A4)', 1650, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Clinical Biochemistry', 'Not enable', 'BHA042', 'Androstein', 'NO', '-', 'Not Verified', NULL),
(547, 'Prolactin', 7780660, 'Prolactin', 550, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHP049', 'PRL', 'NO', '-', 'Not Verified', NULL),
(548, 'Vitamin - B 12', 7780658, 'Vitamin - B 12', 800, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHV011', 'B12', 'NO', '-', 'Not Verified', NULL),
(549, 'Renin Activity', 7780657, 'Renin Activity', 1980, 0, 'SELF', 'EDTA Plasma', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHP026', '-', 'NO', '-', 'Not Verified', NULL),
(552, 'CA-15.3 - Breast Cancer Marker', 7780654, 'CA-15.3 - Breast Cancer Marker', 1265, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Clinical Biochemistry', 'Not enable', 'BHC013', 'CA 15.3', 'NO', '-', 'Not Verified', NULL),
(553, 'Erythropoietin (EPO)', 7780653, 'Erythropoietin (EPO)', 2310, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHE014', '-', 'NO', '-', 'Not Verified', NULL),
(555, 'ASO Titers (Anti Streptolysin O)', 7780651, 'ASO Titers (Anti Streptolysin O)', 330, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHA062', 'ASO-ANTIS', 'NO', '-', 'Not Verified', NULL),
(556, 'Aldolase', 7780650, 'Aldolase', 1320, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHA017', '-', 'NO', '-', 'Not Verified', NULL),
(558, 'Arsenic', 7780648, 'Arsenic', 3300, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHA069', '-', 'NO', '-', 'Not Verified', NULL),
(563, 'Billirubin Total', 7780643, 'Billirubin Total', 110, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHB013', 'BIL', 'NO', '-', 'Not Verified', NULL),
(564, 'CD3/CD4/CD8', 7780642, 'CD3/CD4/CD8', 1320, 0, 'SELF', 'EDTA WB', 'Specialise InHouse TT2', 'Clinical Biochemistry', 'Not enable', '-', 'CD4/CD8', 'NO', '-', 'Not Verified', NULL),
(576, 'Alpha Feto Protein (AFP)', 7780629, 'Alpha Feto Protein (AFP)', 660, 0, 'SELF', 'Serum', 'Specialise InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHA073', 'AFP', 'NO', '-', 'Not Verified', NULL),
(577, 'Liver Function tests (LFT)', 7780628, 'Liver Function tests (LFT)', 660, 0, 'SELF', 'Serum', 'Routine (R)', 'Clinical Biochemistry', 'Not enable', 'HLT0009', 'LFT', 'NO', '-', 'Not Verified', NULL),
(579, 'Haptoglobulin', 7780626, 'Haptoglobulin', 1540, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Clinical Biochemistry', 'Not enable', 'BHH002', '-', 'NO', '-', 'Not Verified', NULL),
(580, 'Adreno Corticotrophic Hormone(ACTH)', 7780625, 'Adreno Corticotrophic Hormone(ACTH)', 1650, 0, 'SELF', 'EDTA Plasma', 'Specialise Outsource TT2', 'Clinical Biochemistry', 'Not enable', 'BHA007', 'ACTH', 'NO', '-', 'Not Verified', NULL),
(581, 'Ceruloplasmin', 7780624, 'Ceruloplasmin', 1100, 0, 'SELF', 'Serum', 'Specialise InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHC044', 'CERULOPLASMIN', 'NO', '-', 'Not Verified', NULL),
(582, 'Urea', 7780622, 'Urea', 165, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHU003', 'UREA', 'NO', '-', 'Not Verified', NULL);

INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) 
VALUES
(583, 'Albumin/Creatinine Ratio', 7780621, 'Albumin/Creatinine Ratio', 220, 0, 'SELF', 'Urine', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHM033', 'MICROalb:Crea', 'NO', '-', 'Not Verified', NULL),
(586, 'Glucose Random', 7780618, 'Glucose Random', 55, 0, 'SELF', 'Plasma - R', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHG030', 'RBS', 'NO', '180Minutes', 'Not Verified', '3:0'),
(589, 'Lipase', 7780615, 'Lipase', 495, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Clinical Biochemistry', 'Not enable', 'BHL017', 'LIP', 'NO', '-', 'Not Verified', NULL),
(260, 'Culture, Aerobic, Throat Swab', 7780954, 'Culture, Aerobic, Throat Swab', 330, 0, 'SELF', 'Swab', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(261, 'Culture and Sensitivity, Urine (22 drugs)', 7780953, 'Culture and Sensitivity, Urine (22 drugs)', 330, 0, 'SELF', 'Urine', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(262, 'Culture, Aerobic, Sputum', 7780952, 'Culture, Aerobic, Sputum', 330, 0, 'SELF', 'Sputum', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(263, 'Culture, Aerobic, Blood', 7780951, 'Culture, Aerobic, Blood', 330, 0, 'SELF', 'BHI Broth/WB Sodium', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(264, 'Culture and Sensitivity - Pus', 7780950, 'Culture and Sensitivity - Pus', 550, 0, 'SELF', 'Pus', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(265, 'Culture, Aerobic, Nasal Swab', 7780949, 'Culture, Aerobic, Nasal Swab', 330, 0, 'SELF', 'Nasal Swab', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(266, 'Culture and Sensitivity - Swab', 7780948, 'Culture and Sensitivity - Swab', 550, 0, 'SELF', 'Eye Swab', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(267, 'Culture and Sensitivity - Urine New Template', 7780947, 'Culture and Sensitivity - Urine', 750.0, 750.0, 'SELF', 'Urine', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '103680Minutes', 'Not Verified', '1728:0'),
(268, 'Culture, Aerobic, Miscellaneous', 7780946, 'Culture, Aerobic, Miscellaneous', 330, 0, 'SELF', 'Tissue(NS)/Body', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(269, 'Culture, Aerobic, Ear Swab', 7780945, 'Culture, Aerobic, Ear Swab', 330, 0, 'SELF', 'Ear Swab', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(270, 'Smear for Diphtheria bacilli', 7780944, 'Smear for Diphtheria bacilli', 220, 0, 'SELF', 'Swab', 'Special (Pathology)', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(271, 'Culture, Aerobic, PUS', 7780943, 'Culture, Aerobic, PUS', 330, 0, 'SELF', 'CSF', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(272, 'Anaerobic Culture (OT Swab)', 7780942, 'Anaerobic Culture (OT Swab)', 1100, 0, 'SELF', 'Swab', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(273, 'Culture Aerobic - Ascitic Fluid', 7780941, 'Culture Aerobic - Ascitic Fluid', 660, 0, 'SELF', 'Ascitic Fluid', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(274, 'Culture, Aerobic, Body Fluids', 7780940, 'Culture, Aerobic, Body Fluids', 330, 0, 'SELF', 'Fluid', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(275, 'Culture and Sensitivity - Stool', 7780939, 'Culture and Sensitivity - Stool', 550, 0, 'SELF', 'Stool', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(276, 'Anaerobic Blood Culture (Automated)', 7780938, 'Anaerobic Blood Culture (Automated)', 3300, 0, 'SELF', 'Whole Blood', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(277, 'Fungus Culture', 7780937, 'Fungus Culture', 6600, 0, 'SELF', 'Swabs', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(278, 'Culture, Aerobic, Semen', 7780936, 'Culture, Aerobic, Semen', 330, 0, 'SELF', 'Semen', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(279, 'NO GROWTH', 7780935, '@', 0.0, 0, 'SELF', 'Swab', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(280, 'GROWTH', 7780934, '@', 0.0, 0, 'SELF', 'Swab', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(281, 'Culture, Aerobic, Vaginal Swab', 7780933, 'Culture, Aerobic, Vaginal Swab', 330, 0, 'SELF', 'Vaginal Swab', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(315, 'Gram Stain', 7780899, 'Gram Stain', 220, 0, 'SELF', 'Fluid', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(460, 'AFB Culture', 7780749, 'AFB Culture', 770, 0, 'SELF', 'Any Specimen', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '43200Minutes', 'Not Verified', '720:0'),
(463, 'Hanging Drop Method', 7780746, 'Hanging Drop Method', 275, 0, 'SELF', 'Stool', 'Clinical Pathology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(473, 'Sputum For Gram Stain', 7780736, 'Sputum For Gram Stain', 330, 0, 'SELF', 'Sputum', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(474, 'Sputum for Malignant Cells', 7780735, 'Sputum for Malignant Cells', 220, 0, 'SELF', 'Swab', 'Microbiology', 'Clinical Microbiology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL);

INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) 
VALUES
(289, 'Absolute Neutrophil Count', 7780925, 'Absolute Neutrophil Count', 165, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'BHA003', 'ANC', 'NO', '-', 'Not Verified', NULL),
(307, 'Activated Partial Thromboplastin Time (APTT/PTTK)', 7780907, 'Activated Partial Thromboplastin Time (APTT/PTTK)', 385, 0, 'SELF', 'Citrated Plasma', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'BHA008', 'APTT', 'NO', '-', 'Not Verified', NULL),
(309, 'Peripheral Smear', 7780905, 'Peripheral Smear', 165, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'BHB021', 'PS', 'NO', '-', 'Not Verified', NULL),
(318, 'Bone Marrow Iron Stain', 7780896, 'Bone Marrow Iron Stain', 990, 0, 'SELF', 'Bone marrow', 'Specialise InHouse TT2', 'Hematology', 'Not enable', 'BHB026', 'BONE MARROW', 'NO', '-', 'Not Verified', NULL),
(320, 'Hemogram', 7780894, 'Hemogram', 330, 0, 'SELF', 'EDTA WB', '-', 'Hematology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(326, 'Hemoglobin', 7780888, 'Hemoglobin', 110, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'BHH018', 'HGB', 'NO', '-', 'Not Verified', NULL),
(345, 'Lupus Anticoagulant', 7780868, 'Lupus Anticoagulant', 1320, 0, 'SELF', 'Citrated Plasma', 'Specialise Outsource TT3', 'Hematology', 'Not enable', 'BHL024', 'LUPUS', 'NO', '-', 'Not Verified', NULL),
(349, 'Coombs Test Indirect', 7780864, 'Coombs Test Indirect', 330, 0, 'SELF', 'Serum', 'Specialise InHouse TT1', 'Hematology', 'Not enable', 'BHC071', 'ICT', 'NO', '-', 'Not Verified', NULL),
(351, 'Osmotic Fragility Test', 7780862, 'Osmotic Fragility Test', 550, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Hematology', 'Not enable', 'BHO008', NULL, 'NO', '-', 'Not Verified', NULL),
(360, 'Micro laria Detection', 7780852, 'Micro laria Detection', 550, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'BHM034', 'MICROFILARIA', 'NO', '-', 'Not Verified', NULL),
(361, 'RH Antibody - Titres', 7780851, 'RH Antibody - Titres', 550, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'BHR011', 'RH', 'NO', '-', 'Not Verified', NULL),
(363, 'Packed Cell Volume', 7780849, 'Packed Cell Volume', 88, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'BHP003', 'PCV', 'NO', '-', 'Not Verified', NULL),
(364, 'Clotting Time', 7780848, 'Clotting Time', 192.5, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'BHC062', 'CT', 'NO', '-', 'Not Verified', NULL),
(366, 'Erythrocyte Sedimentation Rate(ESR)', 7780846, 'Erythrocyte Sedimentation Rate(ESR)', 110, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'BHE015', 'ESR', 'NO', '-', 'Not Verified', NULL),
(367, 'Coombs Test Direct', 7780845, 'Coombs Test Direct', 330, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'BHC070', 'DCT', 'NO', '-', 'Not Verified', NULL),
(370, 'Reticulocyte Count', 7780842, 'Reticulocyte Count', 220, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'BHR003', 'RETICULOCYTE', 'NO', '-', 'Not Verified', NULL),
(374, 'PT (Prothrombin Time)', 7780838, 'PT (Prothrombin Time)', 275, 0, 'SELF', 'Citrated Plasma', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'BHP063', 'PT STUDY', 'NO', '-', 'Not Verified', NULL),
(412, 'Absolute Lymphocyte Count', 7780798, 'Absolute Lymphocyte Count', 165, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'BHA002', 'ALC', 'NO', '-', 'Not Verified', NULL),
(450, 'Platelet Antibodies', 7780760, 'Platelet Antibodies', 6050, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'BHP029', 'PLT', 'NO', '-', 'Not Verified', NULL);


INSERT INTO TestDetails (
    SrNo, 
    TestName, 
    TestID, 
    TestCode, 
    TestAmount, 
    OutsourceAmount, 
    OutsourceCenter, 
    SampleType, 
    TestCategory, 
    DepartmentName, 
    Accreditation, 
    IntegrationCode, 
    ShortText, 
    CAPTest, 
    TargetTAT, 
    VerificationStatus, 
    TargetTATHHMM
) VALUES 
(468, 'WBC Count', 7780741, 'BHT016', 110, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'TLC', 'TLC', 'NO', '-', 'Not Verified', '-'),
(478, 'Factor-IX', 7780731, 'BHF003', 2750, 0, 'SELF', 'Citrated Plasma', 'Specialise Outsource TT3', 'Hematology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(487, 'Plasmodium Falciparum Igs', 7780722, 'BHM008', 3300, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'MALARIA ANTI', 'MALARIA ANTI', 'NO', '-', 'Not Verified', '-'),
(488, 'Thrombin Time', 7780721, 'BHT014', 1100, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Hematology', 'Not enable', 'Trombin Time', 'Trombin Time', 'NO', '-', 'Not Verified', '-'),
(489, 'Malaria Antibody IgM', 7780720, 'BHM007', 1760, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'MP', 'MP', 'NO', '-', 'Not Verified', '-'),
(490, 'Fibrinogen', 7780719, 'BHF015', 550, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Hematology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(516, 'LE Cells', 7780693, 'BHL006', 275, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Hematology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(520, 'Factor-V', 7780689, 'MPF002', 1320, 0, 'SELF', 'EDTA WB', 'Specialise Outsource TT3', 'Hematology', 'Not enable', 'Factor V', 'Factor V', 'NO', '-', 'Not Verified', '-'),
(523, 'Factor-VIII', 7780686, 'BHF006', 1980, 0, 'SELF', 'Citrated Plasma', 'Specialise Outsource TT3', 'Hematology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(525, 'Absolute Esonophil Count', 7780684, 'BHA001', 110, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'AEC', 'AEC', 'NO', '-', 'Not Verified', '-'),
(540, 'Factor-VII', 7780667, 'BHF005', 3850, 0, 'SELF', 'Citrated Plasma', 'Specialise Outsource TT3', 'Hematology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(565, 'Hemoglobinopathy by HPLC (HB Electrophoresis)', 7780641, 'BHH054', 800, 0, 'SELF', 'EDTA WB', 'Specialise InHouse TT1', 'Hematology', 'Not enable', 'HPLC', 'HPLC', 'NO', '-', 'Not Verified', '-'),
(568, 'Erythrocyte Count (RBC Count)', 7780637, 'BHR001', 110, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'RBC', 'RBC', 'NO', '-', 'Not Verified', '-'),
(578, 'Bleeding Time', 7780627, '-', 220, 0, 'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'BT & CT', 'BT & CT', 'NO', '-', 'Not Verified', '-'),
(590, 'Fluid examination-Routine', 7780614, 'BHF060', 275, 0, 'SELF', 'Fluid', 'Routine InHouse TT1', 'Hematology', 'Not enable', 'ROUTINE EXAM.', 'ROUTINE EXAM.', 'NO', '-', 'Not Verified', '-'),
(288, 'Biopsy - Medium Specimen (Upto 5 cm)', 7780926, 'HPH007', 660, 0, 'SELF', 'Tissue', 'Specialise InHouse TT2', 'Histopathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-');

INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) VALUES
(470, 'Tissue Processing (Blocks & Slides)', '7780739', 'Tissue Processing (Blocks & Slides)', 99, 0, 'SELF', 'Tissue', '-', 'Histopathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(471, 'Biopsy - Bone', '7780738', 'Biopsy - Bone', 825, 0, 'SELF', 'Tissue', '-', 'Histopathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(500, 'Biopsy - Uterus with Both tubes & Ovaries', '7780709', 'Biopsy - Uterus with Both tubes & Ovaries', 770, 0, 'SELF', 'Tissue', 'Pathology', 'Histopathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(557, 'Biopsy - Uterus without tubes & Ovaries', '7780649', 'Biopsy - Uterus without tubes & Ovaries', 440, 0, 'SELF', 'Tissue', 'Special (S)', 'Histopathology', 'Not enable', 'HPH005', 'HISTOPATH', 'NO', '-', 'Not Verified', '-'),
(183, 'Prostate Specific Antigen - PSA Total', '7781058', 'Prostate Specific Antigen (PSA Total) - IMN', 700.0, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'Same Day', 'PSA - TOTAL', 'NO', '840Minutes', 'Not Verified', '14:0'),
(192, 'Circulating Immunocomplex (CIC)', '7781049', 'Circulating Immunocomplex (CIC)', 3300, 0, 'SELF', 'Plasma/Serum/Blood', '-', 'Immunology', 'Not enable', '3rd Working Day', 'CIC', 'NO', '840Minutes', 'Not Verified', '14:0'),
(194, 'Gliadin Antibody IgM', '7781047', 'Gliadin Antibody IgM', 2530, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', '3rd Working Day Except Sunday', 'GA-IgG', 'NO', '840Minutes', 'Not Verified', '14:0'),
(195, 'Testosterone - Total', '7781046', 'Testosterone - Total', 495, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'Same Day', 'Testo-Total', 'NO', '840Minutes', 'Not Verified', '14:0'),
(197, 'Immunoglobulin A (lgA)', '7781044', 'Immunoglobulin A (lgA)', 550, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', '3rd Working Day Except Sunday', 'IG-IgA', 'NO', '840Minutes', 'Not Verified', '14:0'),
(198, 'Follicle Stimmulating Hormone (FSH)', '7781043', 'Follicle Stimmulating Hormone (FSH)', 500, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'Same Day', 'FSH', 'NO', '840Minutes', 'Not Verified', '14:0'),
(200, 'Epstein Barr Virus EA IgG', '7781041', 'Epstein Barr Virus EA IgG', 1980, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', '3rd Working Day', 'EBV-IgG', 'NO', '840Minutes', 'Not Verified', '14:0'),
(203, 'Aspergillus Fumigatus IgG', '7781038', 'Aspergillus Fumigatus IgG', 2750, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'Same Day', 'Antiasperg-IgG', 'NO', '840Minutes', 'Not Verified', '14:0'),
(210, '25, OH Vitamin D (Vitamin D2)', '7781029', '25, OH Vitamin D (Vitamin D2)', 1540, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'Same Day', 'D-25', 'NO', '840Minutes', 'Not Verified', '14:0'),
(211, 'Folate serum (folic acid)', '7781028', 'Folate serum (folic acid)', 770, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'Same Day', 'Folate', 'NO', '840Minutes', 'Not Verified', '14:0'),
(212, 'Homocyteine', '7781027', 'Homocyteine', 990, 0, 'SELF', 'EDTA Plasma', '-', 'Immunology', 'Not enable', 'Same Day', 'HOMOCY', 'NO', '840Minutes', 'Not Verified', '14:0'),
(213, 'Beta 2 Mircoglobulin', '7781026', 'Beta 2 Mircoglobulin', 1870, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'Next Working Day Except Sunday', 'Beta2Micro', 'NO', '840Minutes', 'Not Verified', '14:0'),
(217, 'Cortisol Serum (3 to 5 pm)', '7781022', 'Cortisol Serum (3 to 5 pm)', 700, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'Same Day', 'CORTISOL-EVENING', 'NO', '840Minutes', 'Not Verified', '14:0'),
(219, 'Immunoglobulin (lgE)', '7781020', 'Immunoglobulin (lgE)', 6270, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'Same Day', 'T-IgE', 'NO', '840Minutes', 'Not Verified', '14:0'),
(220, 'Epstein Barr Virus EA IgM', '7781019', 'Epstein Barr Virus EA IgM', 1980, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', '3rd Working Day', 'EBV-IgM', 'NO', '840Minutes', 'Not Verified', '14:0'),
(224, 'Anti Smooth Muscle Antibody-Titre(ASTHMA)', '7781014', 'Anti Smooth Muscle Antibody-Titre(ASTHMA)', 2750, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', '3rd Working Day', 'ASMA', 'NO', '840Minutes', 'Not Verified', '14:0'),
(225, 'CA 125-Ovarian Cancer Maker', '7781013', 'CA 125-Ovarian Cancer Maker', 660, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'Same Day', 'CA125', 'NO', '840Minutes', 'Not Verified', '14:0'),
(229, 'Beta Human Chorionic Gonodotropin Hormone(Free)', '7781009', 'Beta Human Chorionic Gonodotropin Hormone(Free)', 1320, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'Next Working Day Except Sunday', 'Beta HCG,Free', 'NO', '840Minutes', 'Not Verified', '14:0'),
(230, 'Prorein Electrophoresis (Oligoclonal Bands)', '7781008', 'Prorein Electrophoresis (Oligoclonal Bands)', 4400, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', '3rd Day Evening Except Sunday', 'SPEP', 'NO', '840Minutes', 'Not Verified', '14:0'),
(231, 'Immunoglobulin G (lgG)', '7781007', 'Immunoglobulin G (lgG)', 550, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', '3rd Working Day Except Sunday', 'IGG', 'NO', '840Minutes', 'Not Verified', '14:0'),
(234, 'Endomysial Antibody IgM', '7781004', 'Endomysial Antibody IgM', 990, 0, 'SELF', 'Serum', '-', 'Immunology', 'Not enable', '3rd Working Day', 'EndoIgA-IFA', 'NO', '840Minutes', 'Not Verified', '14:0');

INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
)
VALUES
    (235, 'Triiodothyronine Free (TT3)', 7781003, 'Triiodothyronine Free (TT3)', 550, 0, 
     'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'FT3', 'TT3', 'NO', 'Same Day', 'Not Verified', '14:0'),
    (237, 'Anti Neutrophilic cytoplasmic antibody(ANCA)Titers', 7781001, 'Anti Neutrophilic cytoplasmic antibody(ANCA)Titers', 2750, 0, 
     'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'ANCA-IFA', 'ANCA', 'NO', '3rd Working Day', 'Not Verified', '14:0'),
    (239, 'Interleukin - 6 (IL -6 )', 7780999, 'Interleukin - 6 (IL -6 )', 2750, 0, 
     'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'IL-6', 'IL6', 'NO', '3rd Working Day', 'Not Verified', '14:0'),
    (241, 'Immunoglobulin M (lgM)', 7780997, 'Immunoglobulin M (lgM)', 550, 0, 
     'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'IGM', 'IgM', 'NO', '3rd Working Day Except Sunday', 'Not Verified', '14:0'),
    (242, 'Prostate Specific Antigen (Total)', 7780996, 'Prostate Specific Antigen (Total)', 660, 0, 
     'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'PSA - TOTAL', 'PSA', 'NO', 'Same Day', 'Not Verified', '14:0'),
    (244, 'Rheumatoid Factor (RA Test) - Quantitative (IgG)', 7780994, 'Rheumatoid Factor (RA Test) - Quantitative (IgG)', 660, 0, 
     'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'RA-IgG', 'RA', 'NO', '3rd Working Day Except Sunday', 'Not Verified', '-'),
    (246, 'Rheumatic Factor IgG Antibody', 7780991, 'Rheumatic Factor IgG Antibody', 660, 0, 
     'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'RH', 'Rheum', 'NO', '3rd Working Day Except Sunday', 'Not Verified', '14:0'),
    (247, 'Hepatitis C Virus (HCV Antibody)', 7780990, 'Hepatitis C Virus (HCV Antibody)', 660, 0, 
     'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'Anti-HES', 'HCV', 'NO', '3rd Working Day', 'Not Verified', '14:0'),
    (249, 'Insulin-Like Growth Factor Binding Protein-3', 7780988, 'Insulin-Like Growth Factor Binding Protein-3', 4400, 0, 
     'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'IGF-I', 'IGF', 'NO', '3rd Working Day Except Sunday', 'Not Verified', '14:0'),
    (8, 'Hepatitis C Virus (Rapid)', 9711391, 'Hepatitis C Virus (HCV Antibody)', 660, 0, 
     'SELF', 'Fluid', '-', 'Immunology', 'Not enable', '-', 'HCV-Rapid', 'NO', '-', 'Not Verified', '-');

INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
)
VALUES
    (99, 'Complement 4 C4', 7781985, 'Complement 4 C4', 1045, 0, 
     'SELF', 'Serum', '-', 'Immunology', 'Not enable', 'Same Day', 'C4', 'NO', '840Minutes', 'Not Verified', '14:0'),
    (100, 'Protein S', 7781970, 'Protein S', 4950, 0, 
     'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHP058', '-', 'NO', '-', 'Not Verified', '-'),
    (105, 'ssDNA Antibody', 7781345, 'ssDNA Antibody', 1760, 0, 
     'SELF', 'Serum', 'Specialise Outsource TT4', 'Immunology & Serology', 'Not enable', 'BHA047', '-', 'NO', '-', 'Not Verified', '-'),
    (129, '5-Dihydrotestrosterone', 7781240, '5-Dihydrotestrosterone', 2750, 0, 
     'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHD010', 'DHT', 'NO', '-', 'Not Verified', '-'),
    (24, 'HIV 1& 2 (Rapid) - Serum', 8453308, 'DIM107', 440, 0, 
     'SELF', 'Serum', 'Pathology', 'Immunology & Serology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
    (291, 'Epstein Barr Virus VCA IgM', 7780923, 'Epstein Barr Virus VCA IgM', 1980, 0, 
     'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHE011', '-', 'NO', '-', 'Not Verified', '-'),
    (300, 'Cardiolipin IgA Antibody', 7780914, 'Cardiolipin IgA Antibody', 550, 0, 
     'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHC036', '-', 'NO', '-', 'Not Verified', '-'),
    (301, 'Inhibin A', 7780913, 'Inhibin A', 2200, 0, 
     'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHI010', 'INHIBIN-A', 'NO', '-', 'Not Verified', '-'),
    (305, 'Salmonella typhi IgG', 7780909, 'Salmonella typhi IgG', 935, 0, 
     'SELF', 'Serum', 'Pathology', 'Immunology & Serology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
    (306, 'Tissue Transglutaminase Antibody - IgM', 7780908, 'Tissue Transglutaminase Antibody - IgM', 2090, 0, 
     'SELF', 'Serum', 'Special (S)', 'Immunology & Serology', 'Not enable', 'BHT038', 'TTG-IgM', 'NO', '-', 'Not Verified', '-'),
    (308, 'Tissue Transglutaminase Antibody - IgA', 7780906, 'Tissue Transglutaminase Antibody - IgA', 2090, 0, 
     'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHT036', 'TTG-IgA', 'NO', '-', 'Not Verified', '-'),
    (310, 'Tissue Transglutaminase Antibody - IgG', 7780904, 'Tissue Transglutaminase Antibody - IgG', 2090, 0, 
     'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHT037', 'TTG-IgG', 'NO', '-', 'Not Verified', '-'),
    (311, 'Parietal Cell Antibody', 7780903, 'Parietal Cell Antibody', 2860, 0, 
     'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHP071', '-', 'NO', '-', 'Not Verified', '-'),
    (312, 'Mantoux Test', 7780902, 'Mantoux Test', 132, 0, 
     'SELF', 'Serum', 'Routine InHouse TT1', 'Immunology & Serology', 'Not enable', 'BHM012', 'MANTOX', 'NO', '-', 'Not Verified', '-'),
    (313, 'Beta 2 Glycoprotein IgM', 7780901, 'Beta 2 Glycoprotein IgM', 1650, 0, 
     'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHB006', '-', 'NO', '-', 'Not Verified', '-'),
    (319, 'Glutamic Acid Decarboxylase Antibody', 7780895, 'Glutamic Acid Decarboxylase Antibody', 2200, 0, 
     'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHG041', '-', 'NO', '-', 'Not Verified', '-'),
    (324, 'Intrinsic Factor', 7780890, 'Intrinsic Factor', 2860, 0, 
     'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHI020', '-', 'NO', '-', 'Not Verified', '-'),
    (325, 'Infectious Mononucleosis', 7780889, 'Infectious Mononucleosis', 605, 0, 
     'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHM050', '-', 'NO', '-', 'Not Verified', '-'),
    (327, 'Mumps Virus Antibody IgM', 7780887, 'Mumps Virus Antibody IgM', 2200, 0, 
     'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHM040', '-', 'NO', '-', 'Not Verified', '-'),
    (328, 'Leishmania Donovani', 7780886, 'Leishmania Donovani', 770, 0, 
     'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHL012', '-', 'NO', '-', 'Not Verified', '-');

INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
)
VALUES
    (330, 'Toxoplasma gondii -IgG', 7780884, 'Toxoplasma gondii -IgG', 220, 0, 
    'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 
    'BHT018', 'TOXO ANTI IgG', 'NO', '-', 'Not Verified', '-'),
    
    (332, 'Beta 2 Glycoprotein IgG', 7780882, 'Beta 2 Glycoprotein IgG', 1650, 0, 
    'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 
    'BHB005', '-', 'NO', '-', 'Not Verified', '-'),
    
    (334, 'Gliadin Antibody IgG', 7780880, 'Gliadin Antibody IgG', 2530, 0, 
    'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 
    'BHG021', '-', 'NO', '-', 'Not Verified', '-'),
    
    (336, 'Rheumatic Factor IgM Antibody', 7780877, 'Rheumatic Factor IgM Antibody', 660, 0, 
    'SELF', 'Serum', 'Routine InHouse TT1', 'Immunology & Serology', 'Not enable', 
    'BHR004', 'RA Quali', 'NO', '-', 'Not Verified', '-'),
    
    (337, 'Widal Test (Slide Test)', 7780876, 'Widal Test (Slide Test)', 275, 0, 
    'SELF', 'Serum', 'Routine InHouse TT1', 'Immunology & Serology', 'Not enable', 
    'BHW002', 'widal slide', 'NO', '-', 'Not Verified', '-'),
    
    (340, 'Cytomegalovirus, IgM, Antibody', 7780873, 'Cytomegalovirus, IgM, Antibody', 660, 0, 
    'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 
    'BHC128', 'CMV IgM', 'NO', '-', 'Not Verified', '-'),
    
    (341, 'Anti Cyclic Citrullinated Peptide Antibody(CCP)', 7780872, 'Anti Cyclic Citrullinated Peptide Antibody(CCP)', 1200, 0, 
    'SELF', 'Serum', 'Routine InHouse TT1', 'Immunology & Serology', 'Not enable', 
    'BHA050', 'ACCP', 'NO', '-', 'Not Verified', '-'),
    
    (348, 'Anti Cysticercosis (IgG) Antibody', 7780865, 'Anti Cysticercosis (IgG) Antibody', 825, 0, 
    'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 
    'BHC168', '-', 'NO', '-', 'Not Verified', '-'),
    
    (353, '17-OH progesterone', 7780860, '17-OH progesterone', 1320, 0, 
    'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 
    'BH1001', '17-OHP', 'NO', '-', 'Not Verified', '-'),
    
    (354, 'Brucella Antibody (B.Abortus & B.Melitensis)', 7780859, 'Brucella Antibody (B.Abortus & B.Melitensis)', 550, 0, 
    'SELF', 'EDTA WB', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 
    'MPB026', '-', 'NO', '-', 'Not Verified', '-'),
    
    (355, 'Anti Smooth Muscle Antibody (ASMA)', 7780858, 'Anti Smooth Muscle Antibody (ASMA)', 1375, 0, 
    'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 
    'BHS016', '-', 'NO', '-', 'Not Verified', '-'),
    
    (356, 'Acetyl Choline Receptor Antibody', 7780857, 'Acetyl Choline Receptor Antibody', 3850, 0, 
    'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 
    'BHA004', '-', 'NO', '-', 'Not Verified', '-'),
    
    (358, 'Anti SS-B Antibody', 7780854, 'Anti SS-B Antibody', 1980, 0, 
    'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 
    'IS0047', '-', 'NO', '-', 'Not Verified', '-'),
    
    (359, 'Anti SS-A Antibody', 7780853, 'Anti SS-A Antibody', 1980, 0, 
    'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 
    'IS0046', '-', 'NO', '-', 'Not Verified', '-'),
    
    (362, 'Cardiolipin IgM Antibody', 7780850, 'Cardiolipin IgM Antibody', 550, 0, 
    'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 
    'BHC038', 'ACA-IGM', 'NO', '-', 'Not Verified', '-'),
    
    (365, 'ANCA - Serine Proteinase 3 (c-ANCA)', 7780847, 'ANCA - Serine Proteinase 3 (c-ANCA)', 1320, 0, 
    'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 
    'BHC131', 'C-ANCA', 'NO', '-', 'Not Verified', '-'),
    
    (368, 'Anti Mullarian Hormone (AMH)', 7780844, 'Anti Mullarian Hormone (AMH)', 2000, 0, 
    'SELF', 'Serum', 'Specialise InHouse TT1', 'Immunology & Serology', 'Not enable', 
    'BHA053', 'AMH', 'NO', '-', 'Not Verified', '-'),
    
    (369, 'Saccharomyces cerevisiae Antibody IgG', 7780843, 'Saccharomyces cerevisiae Antibody IgG', 3300, 0, 
    'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 
    'BHA058', '-', 'NO', '-', 'Not Verified', '-'),
    
    (371, 'Cardiolipin IgG Antibody', 7780841, 'Cardiolipin IgG Antibody', 550, 0, 
    'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 
    'BHC037', 'ACA-IGG', 'NO', '-', 'Not Verified', '-'),
    
    (373, 'Chromosome Analysis (POC)', 7780839, 'Chromosome Analysis (POC)', 6600, 0, 
    'SELF', 'EDTA Plasma', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 
    'BHC0144', '-', 'NO', '-', 'Not Verified', '-');

INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) VALUES
(375, 'Endomysial Antibody IgA', 7780837, 'Endomysial Antibody IgA', 1980, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHE008', '-', 'NO', '-', 'Not Verified', NULL),
(396, 'Anti Phospholipid antibody IgG', 7780815, 'Anti Phospholipid antibody IgG', 550, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHP021', 'APA-IGG', 'NO', '-', 'Not Verified', NULL),
(397, 'Aldosterone', 7780814, 'Aldosterone', 2750, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHA019', '-', 'NO', '-', 'Not Verified', NULL),
(409, 'Hepatitis E Virus -IgG (HEV IgG)', 7780801, 'Hepatitis E Virus -IgG (HEV IgG)', 2200, 0, 'SELF', 'Serum', 'Specialise Outsource TT2', 'Immunology & Serology', 'Not enable', 'BHH034', 'HEV-IGG', 'NO', '-', 'Not Verified', NULL),
(411, 'Rota Virus Antigen', 7780799, 'Rota Virus Antigen', 1650, 0, 'SELF', 'Stool', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHR013', '-', 'NO', '-', 'Not Verified', NULL),
(413, 'Measles (Rubeola) Antibody IgM', 7780797, 'Measles (Rubeola) Antibody IgM', 2200, 0, 'SELF', 'Serum', 'Specialise InHouse TT3', 'Immunology & Serology', 'Not enable', 'BHM020', '-', 'NO', '-', 'Not Verified', NULL),
(416, 'Weil - Felix Test', 7780794, 'Weil - Felix Test', 1100, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHW001', '-', 'NO', '-', 'Not Verified', NULL),
(417, 'Anti Mitochondrial Antibody', 7780793, 'Anti Mitochondrial Antibody', 990, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHM035', '-', 'NO', '-', 'Not Verified', NULL),
(418, 'HIV 1 -DNA PCR - Qualitative', 7780792, 'HIV 1 -DNA PCR - Qualitative', 1650, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', NULL, 'HIV-DUO', 'NO', '-', 'Not Verified', NULL),
(419, 'Anti ds- DNA Antibody Titre', 7780791, 'Anti ds- DNA Antibody Titre', 2200, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHA045', 'DS DNA', 'NO', '-', 'Not Verified', NULL),
(425, 'HIV 1 Western Blot', 7780785, 'HIV 1 Western Blot', 2200, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHW004', 'WESTERN BLOT', 'NO', '-', 'Not Verified', NULL),
(426, 'Parvovirus B 19 Antibody IgM', 7780784, 'Parvovirus B 19 Antibody IgM', 2200, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHP010', '-', 'NO', '-', 'Not Verified', NULL),
(427, 'Hepatitis A Virus IgM', 7780783, 'Hepatitis A Virus IgM', 715, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHH022', 'HAV-IGM', 'NO', '-', 'Not Verified', NULL),
(428, 'SCL-70 Antibody', 7780782, 'SCL-70 Antibody', 1320, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHS001', '-', 'NO', '-', 'Not Verified', NULL),
(430, 'Sm/RNP Antibody', 7780780, 'Sm/RNP Antibody', 1320, 0, 'SELF', 'Serum', 'Specialise InHouse TT3', 'Immunology & Serology', 'Not enable', 'BHR016', '-', 'NO', '-', 'Not Verified', NULL),
(431, 'Glomerular Basement Membrane Antibody (GBM)', 7780779, 'Glomerular Basement Membrane Antibody (GBM)', 1650, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHG022', '-', 'NO', '-', 'Not Verified', NULL),
(432, 'Hepatitis B Surface antigen (HBsAG)-Rapid', 7780778, 'Hepatitis B Surface antigen (HBsAG)-Rapid', 330, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Immunology & Serology', 'Not enable', 'BHH026', 'HBsAg', 'NO', '-', 'Not Verified', NULL),
(436, 'Varicella Zoster IgG', 7780774, 'Varicella Zoster IgG', 2090, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHV003', '-', 'NO', '-', 'Not Verified', NULL),
(440, 'Parvovirus B 19 Antibody IgG', 7780770, 'Parvovirus B 19 Antibody IgG', 2200, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHP009', '-', 'NO', '-', 'Not Verified', NULL),
(441, 'Testosterone - Free', 7780769, 'Testosterone - Free', 880, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHT009', 'TESTO FREE', 'NO', '-', 'Not Verified', NULL);

INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) 
VALUES
(444, 'Ganglioside IgM Antibody (GM1)', 7780766, 'Ganglioside IgM Antibody (GM1)', 2750, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHG010', '-', 'NO', '-', 'Not Verified', NULL),
(451, 'Hepatitis E Virus -IgM (HEV IgM)', 7780758, 'Hepatitis E Virus -IgM (HEV IgM)', 770, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHH035', 'HEP E-IGM', 'NO', '-', 'Not Verified', NULL),
(452, 'Inhibin B', 7780757, 'Inhibin B', 2860, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHI011', '-', 'NO', '-', 'Not Verified', NULL),
(453, 'Chlamydia IgG', 7780756, 'Chlamydia IgG', 2860, 0, 'SELF', 'Serum', 'Specialise Outsource TT1', 'Immunology & Serology', 'Not enable', 'BHC171', '-', 'NO', '-', 'Not Verified', NULL),
(454, 'HIV 1 -RNA PCR - Qualitative', 7780755, 'HIV 1 -RNA PCR - Qualitative', 4950, 0, 'SELF', 'Serum', 'Specialise InHouse TT1', 'Immunology & Serology', 'Not enable', 'WCE494793', 'HIV-ELI', 'NO', '-', 'Not Verified', NULL),
(458, 'Ganglioside IgG Antibody (GM1)', 7780751, 'Ganglioside IgG Antibody (GM1)', 2750, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHG009', '-', 'NO', '-', 'Not Verified', NULL),
(464, 'Rapid Plasma Reagin (RPR Test)', 7780745, 'Rapid Plasma Reagin (RPR Test)', 330, 0, 'SELF', 'Serum', 'Routine InHouse TT1', 'Immunology & Serology', 'Not enable', 'BHV005', 'VDRL (RPR)', 'NO', '-', 'Not Verified', NULL),
(465, 'Rubella Virus - IgM', 7780744, 'Rubella Virus - IgM', 330, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHR008', 'RUBELLA IGM', 'NO', '-', 'Not Verified', NULL),
(466, 'Protein C', 7780743, 'Protein C', 4950, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHP053', '-', 'NO', '-', 'Not Verified', NULL),
(467, 'Epstein Barr Virus VCA IgG', 7780742, 'Epstein Barr Virus VCA IgG', 1980, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHE010', '-', 'NO', '-', 'Not Verified', NULL),
(481, 'Salmonella typhi IgM', 7780728, 'Salmonella typhi IgM', 400, 400, 'SELF', 'Serum', 'Immunology', 'Immunology & Serology', 'Not enable', 'BHT035', 'TYPHI DOT', 'NO', '-', 'Not Verified', NULL),
(484, 'Liver -Kidney Microsome (LKM)- 1 Antibody', 7780725, 'Liver -Kidney Microsome (LKM)- 1 Antibody', 2860, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHL021', 'LKM', 'NO', '-', 'Not Verified', NULL),
(486, 'Anti Jo-I Antibody', 7780723, 'Anti Jo-I Antibody', 1980, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHJ001', '-', 'NO', '-', 'Not Verified', NULL),
(496, 'HIV 1 & 2 Antibody', 7780713, 'HIV 1 & 2 Antibody', 440, 0, 'SELF', 'Serum', 'Pathology', 'Immunology & Serology', 'Not enable', NULL, '-', 'NO', '-', 'Not Verified', NULL),
(503, 'Saccharomyces cerevisiae Antibody IgM', 7780706, 'Saccharomyces cerevisiae Antibody IgM', 3300, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHA057', '-', 'NO', '-', 'Not Verified', NULL),
(510, 'Toxoplasma gondii -IgM', 7780699, 'Toxoplasma gondii -IgM', 220, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHT019', 'TOXO IgM', 'NO', '-', 'Not Verified', NULL),
(518, 'Dengue Ns1', 7780691, 'Dengue Ns1', 1650, 0, 'SELF', 'Serum', 'Routine InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHD020', 'DENGUE ELISA', 'NO', '-', 'Not Verified', NULL),
(526, 'Mumps Virus Antibody IgG', 7780683, 'Mumps Virus Antibody IgG', 2200, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHM039', '-', 'NO', '-', 'Not Verified', NULL),
(538, 'Anti nuclear antibody', 7780669, 'Anti nuclear antibody', 550, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHA091', 'ANA ELISA', 'NO', '-', 'Not Verified', NULL),
(560, 'Herpes Simplex Virus 2 IgM', 7780646, 'Herpes Simplex Virus 2 IgM', 330, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHH037', '-', 'NO', '-', 'Not Verified', NULL);

INSERT INTO testdetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
)
VALUES
(566, 'Cytomegalovirus, IgG, Antibody', 7780640, 'Cytomegalovirus, IgG, Antibody', 385, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHC126', 'CMV -IgG', 'NO', '-', 'Not Verified', NULL),
(567, 'Anti Phospholipid antibody IgM', 7780639, 'Anti Phospholipid antibody IgM', 550, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHP022', 'APA-IGM', 'NO', '-', 'Not Verified', NULL),
(569, 'Chikungunya (IgM & IgG)', 7780636, 'Chikungunya (Rapid)', 780, 0, 'SELF', 'Serum', 'Specialise InHouse TT1', 'Immunology & Serology', 'Not enable', 'BHC045', 'CHIKUMGUNYA IgM', 'NO', '-', 'Not Verified', NULL),
(570, 'Aspergillus Fumigatus IgG(CSF)', 7780635, 'Aspergillus Fumigatus IgG(CSF)', 2750, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHA071', '-', 'NO', '-', 'Not Verified', NULL),
(571, 'Varicella Zoster IgM', 7780634, 'Varicella Zoster IgM', 2200, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'BHV004', '-', 'NO', '-', 'Not Verified', NULL),
(573, 'Anti Neutrophilic cytoplasmic antibody(ANCA)', 7780632, 'Anti Neutrophilic cytoplasmic antibody(ANCA)', 1320, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHA041', 'ANCA', 'NO', '-', 'Not Verified', NULL),
(574, 'Cryoglobulins - Qualitative', 7780631, 'Cryoglobulins - Qualitative', 1100, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 'WHC352619', '-', 'NO', '-', 'Not Verified', NULL),
(587, 'Methamphetamine', 7780617, 'Methamphetamine', 1760, 0, 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', NULL, '-', 'NO', '-', 'Not Verified', NULL),
(588, 'ANCA - Myeloperoxidase (p-ANCA)', 7780616, 'ANCA - Myeloperoxidase (p-ANCA)', 1320, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 'BHP069', 'P-ANCA', 'NO', '-', 'Not Verified', NULL),
(172, 'NO GROWTH (SWAB)', 7781156, '@', 0.0, 0, 'SELF', 'Swab', '-', 'Microbiology', 'Not enable', NULL, '-', 'NO', '-', 'Not Verified', NULL),
(173, 'GROWTH (SWAB)', 7781155, '@', 0.0, 0, 'SELF', 'Swab', '-', 'Microbiology', 'Not enable', NULL, '-', 'NO', '-', 'Not Verified', NULL),
(174, 'OT Swab Aerobic Culture', 7781154, 'OT Swab Aerobic Culture', 385, 0, 'SELF', 'Swab', '-', 'Microbiology', 'Not enable', '3rd Working Day', 'Otswabc/s', 'NO', '840Minutes', 'Not Verified', '14:0'),
(175, 'NO GROWTH (Urine)', 7781153, '@', 0.0, 0, 'SELF', 'Urine', '-', 'Microbiology', 'Not enable', NULL, '-', 'NO', '-', 'Not Verified', NULL),
(176, 'Culture and Sensitivity, Urine', 7781152, 'Culture and Sensitivity, Urine', 750, 0, 'SELF', 'Urine', '-', 'Microbiology', 'Not enable', '3rd Working Day', 'Urine C/S', 'NO', '840Minutes', 'Not Verified', '14:0'),
(177, 'NO GROWTH (STOOL)', 7781151, '@', 0.0, 0, 'SELF', 'Stool', '-', 'Microbiology', 'Not enable', NULL, '-', 'NO', '-', 'Not Verified', NULL),
(178, 'GROWTH (STOOL)', 7781150, '@', 0.0, 0, 'SELF', 'Stool', '-', 'Microbiology', 'Not enable', NULL, '-', 'NO', '-', 'Not Verified', NULL),
(179, 'Culture, Stool', 7781149, 'Culture, Stool', 330, 0, 'SELF', 'Stool', '-', 'Microbiology', 'Not enable', '3rd Working Day', 'Stool C/S', 'NO', '840Minutes', 'Not Verified', '14:0'),
(180, 'NO GROWTH (AFB)', 7781142, '@', 0.0, 0, 'SELF', '-', '-', 'Microbiology', 'Not enable', NULL, '-', 'NO', '-', 'Not Verified', NULL),
(228, 'AFB Stain (Z-N Stain)', 7781010, 'AFB Stain (Z-N Stain)', 275, 0, 'SELF', 'Tissue', '-', 'Microbiology', 'Not enable', 'Same Day', 'ZN Stain', 'NO', '840Minutes', 'Not Verified', '14:0'),
(108, 'Hepatitis C Virus - Viral Load', 7781323, 'Hepatitis C Virus - Viral Load', 5500, 0, 'SELF', 'Serum', 'Special (S)', 'Molecular Biology', 'Not enable', 'MPH010', 'HCV-VL', 'NO', '-', 'Not Verified', NULL);


INSERT INTO testdetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) 
VALUES
(316, 'Factor-V Leiden mutation', 7780898, 'Factor-V Leiden mutation', 5500, 0, 'SELF', 'EDTA WB', 'Specialise Outsource TT3', 'Molecular Biology', 'Not enable', 'MPF002', 'Factor V', 'NO', '-', 'Not Verified', NULL),
(322, 'Mycobacterium Tuberculosis mRNA PCR', 7780892, 'Mycobacterium Tuberculosis mRNA PCR', 3850, 0, 'SELF', 'Tissue', '-', 'Molecular Biology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(329, 'Brucella DNA PCR', 7780885, 'Brucella DNA PCR', 3300, 0, 'SELF', 'EDTA WB', 'Specialise InHouse TT2', 'Molecular Biology', 'Not enable', 'MPB026', NULL, 'NO', '-', 'Not Verified', NULL),
(333, 'Mycobacterium Tuberculosis Complex PCR', 7780881, 'Mycobacterium Tuberculosis Complex PCR', 2750, 0, 'SELF', 'Sputum', '-', 'Molecular Biology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(485, 'Hepatitis B Virus -Qualitative', 7780724, 'Hepatitis B Virus -Qualitative', 3960, 0, 'SELF', 'EDTA Plasma', 'Specialise InHouse TT2', 'Molecular Biology', 'Not enable', 'MPH004', NULL, 'NO', '-', 'Not Verified', NULL),
(494, 'Hepatitis C Virus - Viral Load', 7780715, 'Hepatitis C Virus - Viral Load', 5500, 0, 'SELF', 'Serum', 'Special (S)', 'Molecular Biology', 'Not enable', 'MPH010', 'HCV-VL', 'NO', '-', 'Not Verified', NULL),
(550, 'HLA B-27', 7780656, 'HLA B-27', 2200, 0, 'SELF', 'Fluid', 'Outsource', 'Molecular Biology', 'Not enable', NULL, NULL, 'NO', '-', 'Not Verified', NULL),
(572, 'Hepatitis B Virus Genotyping + Viral Load', 7780633, 'Hepatitis B Virus Genotyping + Viral Load', 11000, 0, 'SELF', 'EDTA Plasma', 'Specialise Outsource TT4', 'Molecular Biology', 'Not enable', 'MPH006', NULL, 'NO', '-', 'Not Verified', NULL),
(585, 'Mycoplasma Pneumonia IgM', 7780619, 'Mycoplasma Pneumonia IgM', 3300, 0, 'SELF', 'Serum', 'Specialise InHouse TT2', 'Molecular Biology', 'Not enable', 'MPM018', NULL, 'NO', '-', 'Not Verified', NULL),
(193, 'Rubella DNA PCR', 7781048, 'Rubella DNA PCR', 2750, 0, 'SELF', 'EDTA WB', '-', 'Molecular Pathology', 'Not enable', '3rd Day evening Except Sunday', 'RubePCR-Qual', 'NO', '840Minutes', 'Not Verified', '14:0'),
(209, 'Chikungunya RNA PCR', 7781030, 'Chikungunya RNA PCR', 3300, 0, 'SELF', 'EDTA WB', '-', 'Molecular Pathology', 'Not enable', '2nd Day Evening Except Sunday', 'Chikun-PCR', 'NO', '840Minutes', 'Not Verified', '14:0'),
(215, 'HIV 1 - Quantitative (Viral Load)', 7781024, 'HIV 1 - Quantitative (Viral Load)', 6600, 0, 'SELF', 'EDTA WB', '-', 'Molecular Pathology', 'Not enable', '4th Working Day', 'Viral Load', 'NO', '840Minutes', 'Not Verified', '14:0'),
(256, 'Measles - PCR', 7780981, 'Measles - PCR', 3850, 0, 'SELF', 'EDTA WB', '-', 'Molecular Pathology', 'Not enable', '3rd Working Day', 'Measles-PCR', 'NO', '840Minutes', 'Not Verified', '14:0'),
 (257, 'Toxoplasma DNA PCR', 7780980, 'Toxoplasma DNA PCR', 2750, 0, 'SELF',   'Plasma/Serum/Blood', '-', 'Molecular Pathology', 'Not enable', '4th Working Day', 'ToxoDNA-Qual', 'NO', '840Minutes', 'Not Verified', '14:0');



INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) 
VALUES
(1, 'CVB MASTER HEALTH CHECK UP', 9904286, 'CVB MAST', 2999.0, 0.0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(102, 'Billirubin (Total + Direct )', 7781391, 'Billirubin (Total + Direct )', 220, 0, 'SELF', 'Serum', 'Pathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(106, 'Prostatic Acid Phosphatase (PAP)', 7781343, 'Prostatic Acid Phosphatase (PAP)', 770, 0, 'SELF', 'Serum', 'Biochemistry', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(109, 'TLC & DLC', 7781321, 'TLC & DLC', 165, 0, 'SELF', 'EDTA WB', 'Haematology', 'Pathology', 'Not enable', '-', 'HB TC DC', 'NO', '-', 'Not Verified', NULL),
(11, 'Anaerobic Blood Culture', 9655390, 'Anaerobic Blood Culture', 1430.0, 0.0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(110, 'Lactate Dehydrogenase (LDH)', 7781316, 'Lactate Dehydrogenase (LDH)', 440, 0, 'SELF', 'Blood', 'Biochemistry', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(111, 'Anti Sperm Antibody (ASAB)', 7781315, 'Anti Sperm Antibody (ASAB)', 1650, 0, 'SELF', 'Semen', 'Clinical Pathology', 'Pathology', 'Not enable', '-', 'ASAB', 'NO', '-', 'Not Verified', NULL),
(112, 'VMA (Vanillyl Mandelic Acid) - SPOT', 7781313, 'VMA (Vanillyl Mandelic Acid) - SPOT', 495, 0, 'SELF', '-', 'Outsource', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(113, 'Transferrin', 7781309, 'Transferrin', 1210, 0, 'SELF', '-', 'Special (Pathology)', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(115, 'Thyoxine Binding Globulin', 7781305, 'Thyoxine Binding Globulin', 1320, 0, 'SELF', '-', 'Biochemistry', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(116, 'Anti B IgG', 7781302, 'Anti B IgG', 1980, 1980, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(117, 'Anti A IgG', 7781301, 'Anti A IgG', 1980, 1980, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(118, 'Hepatitis B Virus IgM', 7781300, 'Hepatitis B Virus IgM', 715, 715, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(119, 'Hepatitis B Virus IgG', 7781298, 'Hepatitis B Virus IgG', 715, 715, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(12, 'Aerobic Blood Culture 5 Days', 9655389, 'Anaerobic Blood Culture 24 Hours', 500, 500, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(120, 'Filaria DNA (Qualitative)', 7781294, 'Filaria DNA (Qualitative)', 2750, 2750, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL);


INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) 
VALUES
(121, 'Factor-X', 7781290, 'Factor-X', 6160, 0, 'SELF', 'Blood', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(122, 'Epstein Barr Virus VCA IgA', 7781287, 'Epstein Barr Virus VCA IgA', 1980, 1980, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(123, 'Epstein Barr Virus EA IgA', 7781285, 'Epstein Barr Virus EA IgA', 1980, 1980, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(124, 'Endomysial Antibody IgG', 7781257, 'Endomysial Antibody IgG', 990, 990, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(125, 'Chamydia IgM', 7781249, 'Chamydia IgM', 2860, 2860, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(127, 'Cardiolipin Antibody (Package)', 7781244, 'Cardiolipin Antibody (Package)', 1650, 0, 'SELF', '-', 'Outsource', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(128, 'CA 125-Ovarian Cancer Maker- Titre', 7781242, 'CA 125-Ovarian Cancer Maker- Titre', 1100, 0, 'SELF', '-', 'Special (Pathology)', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(13, 'Aerobic Blood Culture 48 Hours', 9655388, 'Anaerobic Blood Culture 48 Hours', 500, 500, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(132, 'Defferential Count', 7781231, 'Defferential Count', 165, 0, 'SELF', 'EDTA WB', 'Pathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(133, 'Acid Fast Suspectbility-10 drugs', 7781225, 'Acid Fast Suspectbility-10 drugs', 6820, 0, 'SELF', 'Blood', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(134, 'Acid Fast Suspectbility-5 drugs', 7781224, 'Acid Fast Suspectbility-5 drugs', 4400, 0, 'SELF', 'Blood', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(137, 'Herpes Simplex Virus 1 & 2 IgG', 7781218, 'Herpes Simplex Virus 1 & 2 IgG', 0, 0, 'SELF', '-', 'Outsource', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(138, 'Tuberculosis IgG Antibody', 7781199, 'Tuberculosis IgG Antibody', 275, 0, 'SELF', 'Serum', 'Biochemistry', 'Pathology', 'Not enable', 'BHM012', 'MANTOX', 'NO', '-', 'Not Verified', NULL),
(139, 'Hepatitis B Surface Antigen (HBsAg)', 7781198, 'Hepatitis B Surface Antibody Total (Anti HBs)', 550, 0, 'SELF', 'Serum', 'Immunology', 'Pathology', 'Not enable', 'BHH027', 'HBSAG-HEP B AUSTRALIA', 'NO', '-', 'Not Verified', NULL),
(14, 'Glucose Fasting/PLBS', 9477309, 'glucose f/pp', 100.0, 0.0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(140, '5 - Hdroxy Indole Acetic Acid (5- HIAA)', 7781197, '5 - Hdroxy Indole Acetic Acid (5- HIAA)', 2200, 0, 'SELF', '-', 'Biochemistry', 'Pathology', 'Not enable', '-', '-', 'NO', '10080Minutes', 'Not Verified', '168:0'),
(141, 'Mumps - PCR', 7781196, 'Mumps - PCR', 3850, 0, 'SELF', 'Serum', 'Pathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(142, 'Fluid cell count', 7781195, 'Fluid cell count', 220, 0, 'SELF', '-', 'Biochemistry', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(143, 'Serotonin (5HT)', 7781194, 'Serotonin (5HT)', 3300, 0, 'SELF', 'Blood', 'Pathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(144, 'Anaerobic Swab Culture', 7781193, 'Anaerobic Swab Culture', 825, 0, 'SELF', 'Swab', 'Pathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(145, 'HIV -1 Drug Resistance Assay (PI)', 7781192, 'HIV -1 Drug Resistance Assay (PI)', 20350, 0, 'SELF', 'Blood', 'Pathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(146, 'Treponema Palladium Hemaggalutination (TPHA)', 7781191, 'Treponema Palladium Hemaggalutination (TPHA)', 990, 0, 'SELF', 'Serum', 'Biochemistry', 'Pathology', 'Not enable', 'BHT021', 'TPHA', 'NO', '-', 'Not Verified', NULL),
(147, 'Biopsy - Small Specimen (< 2 cm)', 7781190, 'Biopsy - Small Specimen (< 2 cm)', 550, 0, 'SELF', 'Tissue', 'Pathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(148, 'Rheumatoid Factor (RA Test) - Quantitative (IgM)', 7781187, 'Rheumatoid Factor (RA Test) - Quantitative (IgM)', 440, 0, 'SELF', 'Serum', 'Haematology', 'Pathology', 'Not enable', 'BHR011', 'RH', 'NO', '-', 'Not Verified', NULL),
(149, 'Blood Bag Sterility Test', 7781186, 'Blood Bag Sterility Test', 550, 0, 'SELF', 'Serum', 'Pathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL);


INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) 
VALUES
(15, 'Glucose Fasting/PLBS', 9477307, 'Glucose Fasting/PLBS', 100.0, 0.0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(150, 'Interferon Gamma (IFNY)', 7781185, 'Interferon Gamma (IFNY)', 2200, 0, 'SELF', 'Serum', 'Biochemistry', 'Pathology', 'Not enable', '-', 'TBGOLD', 'NO', '-', 'Not Verified', NULL),
(151, 'Slides for Secondary Opinion', 7781184, 'Slides for Secondary Opinion', 1320, 0, 'SELF', 'Paraffin Blocks', 'Pathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(152, 'Human Papilloma Virus PCR', 7781183, 'Human Papilloma Virus PCR', 3300, 0, 'SELF', 'Serum', 'Biochemistry', 'Pathology', 'Not enable', '-', 'HPV', 'NO', '-', 'Not Verified', NULL),
(153, 'Anaerobic Pus/Body Fluid Culture', 7781182, 'Anaerobic Pus/Body Fluid Culture', 825, 0, 'SELF', 'Fluid', 'Pathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(154, 'Hepatitis C Virus - Genotyping + Viral Load', 7781181, 'Hepatitis C Virus - Genotyping + Viral Load', 12100, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(155, 'Malarial Falciparum & Vivax Antigen (Positive V&F)', 7781180, 'Malarial Falciparum & Vivax Antigen (Positive V&F)', 550, 0, 'SELF', 'EDTA WB', 'Pathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(156, 'Biopsy - Large Specimen (> 5 cm)', 7781179, 'Biopsy - Large Specimen (> 5 cm)', 880, 0, 'SELF', 'Tissue', 'Pathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(157, 'Arterial Blood Gas (ABG)', 7781178, 'Arterial Blood Gas (ABG)', 1100, 0, 'SELF', 'Serum', 'Biochemistry', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(158, 'Aminolevulinic Acid (ALA)', 7781177, 'Aminolevulinic Acid (ALA)', 2750, 0, 'SELF', 'Serum', 'Immunology', 'Pathology', 'Not enable', 'BHA016', '-', 'NO', '-', 'Not Verified', NULL),
(159, 'Anaerobic Stool Culture', 7781176, 'Anaerobic Stool Culture', 825, 0, 'SELF', 'Stool', 'Clinical Pathology', 'Pathology', 'Not enable', 'BHS025', '-', 'NO', '-', 'Not Verified', NULL),
(160, 'Chikungunya IgG', 7781175, 'Chikungunya IgG', 780, 0, 'SELF', 'Serum', 'Immunology', 'Pathology', 'Not enable', 'BHC045', 'CHIKUMGUNYA IgM', 'NO', '-', 'Not Verified', NULL),
(161, 'Herpes Simplex Virus 1 & 2 IgM', 7781174, 'Herpes Simplex Virus 1 & 2 IgM', 440, 0, 'SELF', 'Serum', 'Immunology', 'Pathology', 'Not enable', 'BHH039', '-', 'NO', '-', 'Not Verified', NULL),
(162, 'Blood Picture - Peripheral Smear Examination', 7781173, 'Blood Picture - Peripheral Smear Examination', 165, 0, 'SELF', 'EDTA WB', 'Haematology', 'Pathology', 'Not enable', 'BHB021', 'PBS', 'NO', '1440Minutes', 'Not Verified', '24:0'),
(163, 'Hepatitis C Virus Qualitative', 7781172, 'Hepatitis C Virus Qualitative', 2750, 0, 'SELF', 'Serum', 'Immunology', 'Pathology', 'Not enable', 'MPH010', '-', 'NO', '-', 'Not Verified', NULL),
(164, 'Malarial Parasite Identification', 7781170, 'Malarial Parasite Identification', 165, 0, 'SELF', 'EDTA WB', 'Pathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(165, 'HIV -1 Drug Resistance Assay (RT)', 7781169, 'HIV -1 Drug Resistance Assay (RT)', 20350, 0, 'SELF', 'Serum', 'Pathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(166, 'Carcino Embryonic Antigen (CEA)', 7781168, 'Carcino Embryonic Antigen (CEA)', 660, 0, 'SELF', 'Serum', 'Biochemistry', 'Pathology', 'Not enable', 'BHC042', 'CEA', 'NO', '-', 'Not Verified', NULL),
(167, 'Cytomegalovirus, DNA PCR', 7781167, 'Cytomegalovirus, DNA PCR', 4400, 0, 'SELF', 'Serum', 'Biochemistry', 'Pathology', 'Not enable', '-', 'CMV IgG', 'NO', '-', 'Not Verified', NULL),
(168, 'Hepatitis B Virus Envelop Antibody (Anti Hbe)', 7781166, 'Hepatitis B Virus Envelop Antibody (Anti Hbe)', 550, 0, 'SELF', 'Serum', 'Immunology', 'Pathology', 'Not enable', 'BHH028', '-', 'NO', '-', 'Not Verified', NULL),
(169, 'Herpes Simplex Virus 2 IgG', 7781165, 'Herpes Simplex Virus 2 IgG', 330, 0, 'SELF', 'Serum', 'Immunology', 'Pathology', 'Not enable', 'BHH036', 'HSV 1-IGG', 'NO', '-', 'Not Verified', NULL),
(170, 'Lipid Profile', 7781164, 'Lipid Profile', 660, 0, 'SELF', 'Serum', 'Biochemistry', 'Pathology', 'Not enable', 'BH107G', 'LPD', 'NO', '-', 'Not Verified', NULL),
(171, 'Albert Stain (C.diphtheriae)', 7781163, 'Albert Stain (C.diphtheriae)', 220, 0, 'SELF', 'Serum', 'Microbiology', 'Pathology', 'Not enable', 'MCA060', '-', 'NO', '-', 'Not Verified', NULL),
(18, 'Oral Glucose Tolerance Test - profile', 9470123, 'OGTT', 450.0, 0.0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(181, 'PSA Total : PSA Free Ratio', 7781060, 'PPL464', 1500.0, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', 'Same Day', 'PSA TOTAL / FREE RATIO', 'NO', '840Minutes', 'Not Verified', '14:0');


INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) 
VALUES
(184, 'Prostate Specific Antigen (Free)', 7781057, 'Prostate Specific Antigen (Free)', 1100, 0, 'SELF', '-', '-', 'Pathology', 'Not enable', 'Same Day', '-', 'NO', '14Minutes', 'Not Verified', '0:14'),
(19, 'Fever Profile - II', 9452854, 'fever profile 2', 599.0, 0.0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(190, '17-Ketosteroids', 7781051, '17-Ketosteroids', 3850, 0, 'SELF', '24Hr. Urine', '-', 'Pathology', 'Not enable', '5th Working Day', '17-OH', 'NO', '840Minutes', 'Not Verified', '14:0'),
(2, 'Minor Surgical Profile', 9894058, 'MSP', 1800.0, 0.0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(20, 'Fever Profile - I', 9452853, 'Fever Profile - I', 1999.0, 0.0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(21, 'Immunity Profile', 9452852, 'Immunity, Iron', 899.0, 0.0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(22, 'Joint Pain Profile', 9452851, 'Joint, Pain', 1199.0, 0.0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(23, 'Doctor Consultation', 8755747, 'Doctor Consultation', 400, 300, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(243, 'Thyroid Profile ( T3, T4, TSH)', 7780995, 'Thyroid Profile ( T3, T4, TSH)', 495, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', 'Same Day', 'TFT', 'NO', '840Minutes', 'Not Verified', '14:0'),
(26, 'Dengue Fever Profile-3 , Rapid (Includes Ns1, Igg, Igm)', 8453304, 'Dengue Fever Profile-3 , Rapid (Includes Ns1, Igg, Igm)', 1650, 0, 'SELF', 'Serum', 'Pathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(27, 'Dengue Antibody, Ns1(Rapid)', 8453303, 'Dengue Antibody, NS1(Rapid) - D007', 1650, 0, 'SELF', 'Serum', 'Biochemistry', 'Pathology', 'Not enable', 'BHD004', 'DNS1', 'NO', '-', 'Not Verified', '-'),
(28, 'Anti Thyroid Peroxidase (Anti TPO)', 8234656, 'Anti Thyroid Peroxidase (Anti TPO)', 950, 0, 'SELF', 'Serum', 'Immunology', 'Pathology', 'Not enable', 'BHA065', 'ANTI TPO', 'NO', '-', 'Not Verified', '-'),
(29, 'Blood Group ABO & Rh Typing, Blood', 8048895, 'Blood Group ABO & Rh Typing, Blood - B020', 100, 0, 'SELF', 'EDTA WB', 'Haematology', 'Pathology', 'Not enable', 'BHB020', 'BF : ABO & RH', 'NO', '-', 'Not Verified', '-'),
(3, 'BT/CT', 9893069, 'Bleeding Time and clotting time', 140.0, 0.0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(31, 'Electrolytes, Serum', 7875665, 'Electrolytes, Serum - E007', 450, 0, 'SELF', 'Serum', 'Biochemistry', 'Pathology', 'Not enable', 'BHE007', 'ELECTRO', 'NO', '180Minutes', 'Not Verified', '3:0'),
(32, 'Master Health Check up', 7851514, 'Master Package', 2999.0, 0.0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '0.00Minutes', 'Not Verified', '0:0'),
(33, 'Senior Citizens Health check up', 7851513, 'Senior Citizens', 819.0, 0.0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(34, 'Diabetic Panel', 7851512, 'Sugar Panel', 799.0, 0.0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '0.00Minutes', 'Not Verified', '0:0'),
(35, 'Senior Citizens health check up', 7850144, 'Senior', 929.0, 0.0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(36, 'Basic Health Check-up', 7850124, 'BHC', 1039.0, 0.0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(37, 'Bone Marrow Examination (opinion)', 7782052, 'Bone Marrow Examination (opinion)', 990, 0, 'SELF', '-', 'Histopathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(38, 'Biopsy -Extra,Large Specimen', 7782051, 'Biopsy -Extra,Large Specimen', 1320, 0, 'SELF', '-', 'Histopathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(39, 'Biopsy - Uterus with One tubes & Ovaries', 7782050, 'Biopsy - Uterus with One tubes & Ovaries', 550, 0, 'SELF', '-', 'Histopathology', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-'),
(40, 'Gomori''s Methenamine Silver Stain', 7782048, 'Gomori''s Methenamine Silver Stain', 1100, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', '-');


INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) 
VALUES
(41, 'Entamoeba Histolytica Detection', 7782047, 'Entamoeba Histolytica Detection', 440, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(42, 'Fetal Autopsy', 7782046, 'Fetal Autopsy', 3300, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(43, 'Cervical Swab Culture', 7782045, 'Cervical Swab Culture', 330, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(44, 'Allergy Profile - Food (Vegetarian Only)', 7782044, 'Allergy Profile - Food (Vegetarian Only)', 3850, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(45, 'Fluid Examination (CAPD)', 7782043, 'Fluid Examination (CAPD)', 110, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(46, 'Chromosome Breakage Analysis', 7782042, 'Chromosome Breakage Analysis', 7700, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(47, 'Tuberculosis Gold Quantiferon', 7782041, 'Tuberculosis Gold Quantiferon', 2750, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(48, 'CD4/C8 Count', 7782040, 'CD4/C8 Count', 1540, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(49, 'Citric Acid', 7782039, 'Citric Acid', 550, 0, 'SELF', 'Whole blood', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(5, 'CVB Vitality Membership Free Tests', 9865286, 'CVB VM FREE', 0.0, 0.0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', 'PRIOR TO THIS TEST, YOU WILL OFTEN NEED TO FAST FOR EIGHT TO TWELVE HOURS', 'NO', '-', 'Not Verified', NULL),
(50, 'Fasting Urine Glucose', 7782038, 'Fasting Urine Glucose', 165, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(51, 'BCR - ABL', 7782037, 'BCR - ABL', 6600, 0, 'SELF', 'Whole blood', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(52, 'Allergy Profile - Food (Non-Veg only)', 7782036, 'Allergy Profile - Food (Non-Veg only)', 3850, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(53, 'AFB Identification/Differentiation of MTB/MOTT', 7782035, 'AFB Identification/Differentiation of MTB/MOTT', 3300, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(54, 'Dopamine (Urine)', 7782034, 'Dopamine (Urine)', 2750, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(55, 'Dopamine (Plasma)', 7782033, 'Dopamine (Plasma)', 2420, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(56, 'Allergy Profile - Inhalants', 7782032, 'Allergy Profile - Inhalants', 3850, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(57, 'BCR-ABL(9:22) Qualitative', 7782031, 'BCR-ABL(9:22) Qualitative', 3932.5, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(58, 'Stem Cell Enumeration (CD34+/CD45+)', 7782030, 'Stem Cell Enumeration (CD34+/CD45+)', 4950, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(59, 'Urine for Culture (Automated)', 7782029, 'Urine for Culture (Automated)', 1100, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(6, 'CVB Vitality Membership CARD', 9834785, 'CVB Vitality Membership CARD', 500.0, 500.0, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', 'CVBVM', 'CVBVM', 'NO', '-', 'Not Verified', NULL),
(60, 'Stool Clostridium Difficile Toxin', 7782028, 'Stool Clostridium Difficile Toxin', 880, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(61, 'ICU Swab Aerobic Culture', 7782027, 'ICU Swab Aerobic Culture', 385, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(62, 'Methemoglobinuria', 7782026, 'Methemoglobinuria', 1650, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(63, 'Acetaminophen', 7782025, 'Acetaminophen', 1650, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL);


INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) 
VALUES
(64, 'Carboxyhemoglobin', 7782024, 'Carboxyhemoglobin', 0, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(65, 'Culture Aerobic - Blood(BD Bactec)', 7782023, 'Culture Aerobic - Blood(BD Bactec)', 660, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(66, 'Helicobactor Pyloti PCR', 7782022, 'Helicobactor Pyloti PCR', 3850, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(67, 'Pandys Test', 7782021, 'Pandys Test', 880, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(68, 'Culture Aerobic, Eye Swb', 7782020, 'Culture Aerobic, Eye Swb', 330, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(69, 'Plasmodium IgM', 7782019, 'Plasmodium IgM', 550, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(7, 'ANA PROFILE', 9714502, 'CVBANAP', 4285, 0, 'SELF', '-', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(70, 'Gonorrhea', 7782018, 'Gonorrhea', 330, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(71, 'CD34 Enumeration', 7782017, 'CD34 Enumeration', 5500, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(72, 'Organochloride', 7782016, 'Organochloride', 6600, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(73, 'Gastric Fluid Analysis', 7782015, 'Gastric Fluid Analysis', 1100, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(74, 'Cytology - FNAC (slides for opinion)', 7782014, 'Cytology - FNAC (slides for opinion)', 330, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(75, 'Culture, Aerobic, CSF', 7782013, 'Culture, Aerobic, CSF', 330, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(76, 'Organophosphorus', 7782012, 'Organophosphorus', 2750, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(77, 'Cytology - FNAC (procedure, staining and reporting)', 7782011, 'Cytology - FNAC (procedure, staining and reporting)', 770, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(78, 'Heparin/PF4 Antibody (HIT)', 7782010, 'Heparin/PF4 Antibody (HIT)', 2750, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(79, 'Barr Bodies', 7782009, 'Barr Bodies', 330, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(80, 'Leptospria DNA PCR', 7782008, 'Leptospria DNA PCR', 3850, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(81, 'Human T-Cell Lymphotropic Viruses Antibody 1&2', 7782007, 'Human T-Cell Lymphotropic Viruses Antibody 1&2', 4400, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(82, 'Immuno Histochemistry Marker (Single)', 7782006, 'Immuno Histochemistry Marker (Single)', 1760, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(83, 'Chromosome Analysis (except POC)', 7782005, 'Chromosome Analysis (except POC)', 4950, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(84, 'Syphilis IgG + IgM', 7782004, 'Syphilis IgG + IgM', 990, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(85, 'Allergy Profile', 7782003, 'Allergy Profile', 5500, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(86, 'Acid Fast Susceptibility (20 Drugs)', 7782002, 'Acid Fast Susceptibility (20 Drugs)', 15950, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(87, 'Histone Antibody IgG', 7782001, 'Histone Antibody IgG', 1650, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(88, 'Allergy Profile - Food (Veg + Non-Veg)', 7782000, 'Allergy Profile - Food (Veg + Non-Veg)', 3850, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(89, 'Amoeba Trophozoites', 7781999, 'Amoeba Trophozoites', 1320, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(9, 'NO GROWTH new template', 9685932, 'NO GROWTH new template', 0, 0, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(90, 'Extended Antibiogram', 7781998, 'Extended Antibiogram', 770, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL);



INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) 
VALUES
(91, 'Auramine Rhaodamine Stain', 7781997, 'Auramine Rhaodamine Stain', 385, 0, 'SELF', 'Serum', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(92, 'Tuberculosis IgM Antibody', 7781996, 'Tuberculosis IgM Antibody', 275, 275, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(93, 'Tuberculosis IgA Antibody', 7781995, 'Tuberculosis IgA Antibody', 275, 275, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(94, 'Syphilis Antibodies ( IgM, IgG)', 7781994, 'Syphilis Antibodies ( IgM, IgG)', 550, 550, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(95, 'Syphilis Antibodies (IgG) or IgM', 7781993, 'Syphilis Antibodies (IgG) or IgM', 275, 275, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(96, 'Syphilis IgM', 7781992, 'Syphilis IgM', 550, 550, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(97, 'Syphilis IgG', 7781991, 'Syphilis IgG', 550, 550, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(98, 'Aerobic Blood Culture 24 Hours', 7781988, 'Anaerobic Blood Culture 24 Hours', 500, 500, 'SELF', 'Fluid', '-', 'Pathology', 'Not enable', '-', '-', 'NO', '-', 'Not Verified', NULL),
(182, 'Prostate Specific Antigen - PSA Free', 7781059, 'PPL464', 900, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', 'Same Day', 'PSA-FREE', 'NO', '840Minutes', 'Not Verified', '14:0'),
(185, 'Helicobacter Pylori IgM', 7781056, 'Helicobacter Pylori IgM', 2530, 0, 'SELF', 'Stool', '-', 'Serology', 'Not enable', '2nd Working Day Except Sunday', 'HPA-Stool', 'NO', '840Minutes', 'Not Verified', '14:0'),
(187, 'Herpes Simplex Virus 1 & 2 DNA PCR', 7781054, 'Herpes Simplex Virus 1 & 2 DNA PCR', 5500, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', 'Same Day', 'HSV1&2-IgG', 'NO', '840Minutes', 'Not Verified', '14:0'),
(189, 'Dengue IgG', 7781052, 'Dengue IgG', 1650, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', 'Same Day', 'Dengue-IgG(Qual.)', 'NO', '840Minutes', 'Not Verified', '14:0'),
(191, 'Leptospira Antibody IgG', 7781050, 'Leptospira Antibody IgG', 1320, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', '3rd Working Day', 'AntiLepto-IgG', 'NO', '840Minutes', 'Not Verified', '14:0'),
(201, 'Widal Test (Tube Test)', 7781040, 'Widal Test (Tube Test)', 550, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', 'Same Day', 'widal slide', 'NO', '840Minutes', 'Not Verified', '14:0'),
(202, 'Herpes Simplex Virus 1 IgM', 7781039, 'Herpes Simplex Virus 1 IgM', 330, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', 'Same Day', 'HSV1&2-IgM', 'NO', '840Minutes', 'Not Verified', '14:0'),
(204, 'Hepatitis Anti Core Antibody IgM (Anti HBc - IgM)', 7781037, 'Hepatitis Anti Core Antibody IgM (Anti HBc - IgM)', 550, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', '3rd Working Day', 'AntiHBc-IgM', 'NO', '840Minutes', 'Not Verified', '14:0'),
(207, 'Dengue-RNA PCR', 7781032, 'Dengue-RNA PCR', 4400, 0, 'SELF', 'EDTA WB', '-', 'Serology', 'Not enable', '3rd Day evening Except Sunday', 'DengueRNA-Qual', 'NO', '840Minutes', 'Not Verified', '14:0'),
(208, 'Hepatitis A Virus - Total (HAV - Total)', 7781031, 'Hepatitis A Virus - Total (HAV - Total)', 1760, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', 'Same Day', 'HAVIgG', 'NO', '840Minutes', 'Not Verified', '14:0'),
(216, 'Echinococcus Antibody, IgG (Hydatid serology)', 7781023, 'Echinococcus Antibody, IgG (Hydatid serology)', 2200, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', '3rd Working Day', 'Hydatid-IgG', 'NO', '840Minutes', 'Not Verified', '14:0'),
(218, 'Hepatitis AntiCore Antibody Total (HBcAb-Total)', 7781021, 'Hepatitis AntiCore Antibody Total (HBcAb-Total)', 550, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', '3rd Working Day', 'AntiHBc-Total', 'NO', '840Minutes', 'Not Verified', '14:0'),
(222, 'Dengue IgM', 7781017, 'Dengue IgM', 1650, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', 'Same Day', 'Dengue-IgM(Quan.)', 'NO', '840Minutes', 'Not Verified', '14:0');



INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) 
VALUES
(223, 'Dhydroepiandrosterone sulphate (DHEAS)', 7781016, 'Dhydroepiandrosterone sulphate (DHEAS)', 605, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', 'Same Day', 'DHEAS', 'NO', '840Minutes', 'Not Verified', '14:0'),
(236, 'Brucella Antibody (IgG or IgM) each one', 7781002, 'Brucella Antibody (IgG or IgM) each one', 880, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', '3rd Day Evening Except Sunday', 'Bruce-IgG&IgM', 'NO', '840Minutes', 'Not Verified', '14:0'),
(238, 'Chikungunya IgM', 7781000, 'Chikungunya IgM', 780, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', '2nd Day Evening Except Sunday', 'Chikung-IgM', 'NO', '840Minutes', 'Not Verified', '14:0'),
(240, 'Helicobactor Pyloti IgG', 7780998, 'Helicobactor Pyloti IgG', 2530, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', '3rd Working Day', 'Helico-IgM', 'NO', '840Minutes', 'Not Verified', '14:0'),
(245, 'Hepatitis B Virus Envelop Antigene (HBeAG)', 7780992, 'Hepatitis B Virus Envelop Antigene (HBeAG)', 550, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', '3rd Working Day', 'Anti Hbe', 'NO', '840Minutes', 'Not Verified', '14:0'),
(248, 'Hepatitis B Surface antigen (HBsAG)', 7780989, 'Hepatitis B Surface antigen (HBsAG)', 440, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', 'Same Day', 'HBsAg ELISA', 'NO', '840Minutes', 'Not Verified', '14:0'),
(250, 'Entamoeba histolytica(amoebiasis), Antibody IgG', 7780987, 'Entamoeba histolytica(amoebiasis), Antibody IgG', 2090, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', '3rd Working Day', 'Amorbic-IgG', 'NO', '840Minutes', 'Not Verified', '14:0'),
(252, 'Leptospria Antibody IgM', 7780985, 'Leptospria Antibody IgM', 1320, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', '3rd Working Day', 'AntiLepto-IgM', 'NO', '840Minutes', 'Not Verified', '14:0'),
(254, 'Cryptococcus Antigen', 7780983, 'Cryptococcus Antigen', 2860, 0, 'SELF', 'Stool', '-', 'Serology', 'Not enable', '2nd Working Day Except Sunday', 'Crypto Anti- Stool', 'NO', '840Minutes', 'Not Verified', '14:0'),
(255, 'Dihydroepiandrosterone (DHEA)', 7780982, 'Dihydroepiandrosterone (DHEA)', 3740, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', '3rd Working Day', 'DHEA', 'NO', '840Minutes', 'Not Verified', '14:0'),
(259, 'Anti nuclear antibody-Titers', 7780978, 'Anti nuclear antibody-Titers', 1100, 0, 'SELF', 'Serum', '-', 'Serology', 'Not enable', '4th Working Day', 'ANA-Titres', 'NO', '840Minutes', 'Not Verified', '14:0');

INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
)
VALUES (
    4, 'Antenatal Profile', 9877385, 'ANP', 1999.0, 0.0, 
    'SELF', '-', '-', 'Pathology', 'Not enable', 
    '-', '-', 'NO', '-', 'Not Verified', '-'
);


INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
)
VALUES 
(25, 'Malarial Falciparum and Vivax Antigen (Parasite V & F)', 8453307, 'DHM037', 0, 0, 
    'SELF', 'EDTA WB', 'Routine InHouse TT1', 'Hematology', 'Not enable', 
    'BHM008', 'MALARIA ANTI', 'NO', '-', 'Not Verified', '-'),
(101, 'Fibrinogen Deradation Product (FDP)', 7781410, 'Fibrinogen Deradation Product (FDP)', 935, 0, 
    'SELF', 'Serum', 'Specialise Outsource TT3', 'Hematology', 'Not enable', 
    'BHF014', '-', 'NO', '-', 'Not Verified', '-'),
(114, 'Activated Protein C Resistance (APCR)', 7781306, 'Activated Protein C Resistance (APCR)', 4950, 0, 
    'SELF', 'Citrate plasma', '-', 'Haematology', 'Not enable', 
    '-', '-', 'NO', '10080Minutes', 'Not Verified', '168:0');

INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
)
VALUES 
(135, 'Absolute Basophil Count', 7781220, 'Absolute Basophil Count', 0.0, 0, 
    'SELF', 'Whole blood', 'Hematology', 'Haematology', 'Not enable', 
    '-', '-', 'NO', '420Minutes', 'Not Verified', '7:0'),
(136, 'Absolute Monocyte Count', 7781219, 'Absolute Monocyte Count', 0.0, 0, 
    'SELF', 'Whole blood', '', 'Haematology', 'Not enable', 
    '-', '-', 'NO', '480Minutes', 'Not Verified', '8:0'),
(186, 'Malaria Antibody IgG', 7781055, 'Malaria Antibody IgG', 1760, 0, 
    'SELF', 'EDTA WB', '', 'Haematology', 'Not enable', 
    'Same Day', 'PSforMP', 'NO', '840Minutes', 'Not Verified', '14:0'),
(205, 'Cytomegalovirus, IgM, Antibody', 7781035, 'Cytomegalovirus, IgM, Antibody', 385, 0, 
    'SELF', 'Serum', '', 'Haematology', 'Not enable', 
    'Same Day', 'CMV IgM', 'NO', '840Minutes', 'Not Verified', '14:0'),
(206, 'PT and APTT', 7781033, 'PT and APTT', 550, 0, 
    'SELF', 'Citrated Plasma', '', 'Haematology', 'Not enable', 
    'Same Day', 'APTT', 'NO', '840Minutes', 'Not Verified', '14:0'),
(226, 'Complete Blood Count (CBC)', 7781012, 'Complete Blood Count (CBC)', 300, 275, 
    'SELF', 'EDTA WB', '', 'Haematology', 'Not enable', 
    'Same Day', 'CBC', 'NO', '840Minutes', 'Not Verified', '14:0'),
(227, 'Giemsa Stain', 7781011, 'Giemsa Stain', 110, 0, 
    'SELF', 'Tissue', '', 'Haematology', 'Not enable', 
    'Same Day', 'Giemsa', 'NO', '840Minutes', 'Not Verified', '14:0'),
(253, 'Platelet Count', 7780984, 'Platelet Count', 110, 0, 
    'SELF', 'EDTA WB', '', 'Haematology', 'Not enable', 
    'Same Day', 'PLT', 'NO', '840Minutes', 'Not Verified', '14:0'),
(284, 'Sickling Test', 7780930, 'Sickling Test', 275, 0, 
    'SELF', 'EDTA WB', 'Specialise Outsource TT3', 'Hematology', 'Not enable', 
    'BHS011', 'SICK', 'NO', '-', 'Not Verified', '-');

INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) VALUES
(335, 'Oral Glucose Tolerance Test (O-GTT)', 7780879, 'Oral Glucose Tolerance Test (O-GTT)', 440, 0, 
 'SELF', 'Fluoride Plasma', 'Clinical Pathology', 'Clinical Pathology', 'Not enable', 
 'BH168G', 'CLU TOL', 'NO', '-', 'Not Verified', '-'),

(338, 'Pap Smear', 7780875, 'Pap Smear', 550, 0, 
 'SELF', 'Fluid', 'Specialise InHouse TT2', 'Cytopathology', 'Not enable', 
 'CYL001', 'LBC', 'NO', '-', 'Not Verified', '-'),

(346, 'Semen for Fructose', 7780867, 'Semen for Fructose', 550, 0, 
 'SELF', 'Semen', 'Routine InHouse TT1', 'Clinical Pathology', 'Not enable', 
 'BHS006', '-', 'NO', '-', 'Not Verified', '-'),

(491, 'Pregnancy Test', 7780718, 'Pregnancy Test', 165, 0, 
 'SELF', 'Urine', 'Routine InHouse TT1', 'Clinical Pathology', 'Not enable', 
 'BHP043', 'UPT', 'NO', '-', 'Not Verified', '-'),

(357, 'Stool Examination', 7780856, 'Stool Examination', 110, 0, 
 'SELF', 'Stool', 'Routine InHouse TT1', 'Clinical Pathology', 'Not enable', 
 'BHS027', 'ROUTINE', 'NO', '-', 'Not Verified', '-'),

(372, 'HER - 2/neu', 7780840, 'HER - 2/neu', 3960, 0, 
 'SELF', 'Paraffin Block', 'Specialise InHouse TT2', 'Cytogenetics', 'Not enable', 
 'CGC006', '-', 'NO', '-', 'Not Verified', '-'),

(459, 'Cytology - Fluids', 7780750, 'Cytology - Fluids', 550, 0, 
 'SELF', 'Fluid', '-', 'Cytopathology', 'Not enable', 
 '-', '-', 'NO', '-', 'Not Verified', '-'),

(492, 'Hepatitis A Virus IgG', 7780717, 'Hepatitis A Virus IgG', 715, 0, 
 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 
 'BHH021', '-', 'NO', '-', 'Not Verified', '-'),

(493, 'Hepatitis C Virus IgG', 7780716, 'Hepatitis C Virus IgG', 550, 0, 
 'SELF', 'Serum', 'Routine InHouse TT2', 'Immunology & Serology', 'Not enable', 
 'BHH030', 'Anti HCV HEP C', 'NO', '-', 'Not Verified', '-'),

(508, 'Rubella Virus - IgG', 7780701, 'Rubella Virus - IgG', 330, 0, 
 'SELF', 'Serum', 'Specialise InHouse TT2', 'Immunology & Serology', 'Not enable', 
 'BHR007', 'RUBELLA ANTI IgG', 'NO', '-', 'Not Verified', '-');

INSERT INTO TestDetails (
    SrNo, TestName, TestID, TestCode, TestAmount, OutsourceAmount, 
    OutsourceCenter, SampleType, TestCategory, DepartmentName, Accreditation, 
    IntegrationCode, ShortText, CAPTest, TargetTAT, VerificationStatus, TargetTATHHMM
) VALUES
(511, 'Anti Musk Antibodies', 7780698, 'Anti Musk Antibodies', 7260, 0, 
 'SELF', 'Serum', 'Specialise InHouse TT3', 'Immunology & Serology', 'Not enable', 
 NULL, '-', 'NO', '-', 'Not Verified', '-'),

(517, 'Aspergillus Fumigatus IgM', 7780692, 'Aspergillus Fumigatus IgM', 2750, 0, 
 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 
 'BHA072', '-', 'NO', '-', 'Not Verified', '-'),

(527, 'Measles (Rubeola) IgG', 7780682, 'Measles (Rubeola) IgG', 2200, 0, 
 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 
 'BHM019', '-', 'NO', '-', 'Not Verified', '-'),

(528, 'Herpes Simplex Virus 1 IgG', 7780681, 'Herpes Simplex Virus 1 IgG', 330, 0, 
 'SELF', 'Serum', 'Specialise InHouse TT1', 'Immunology & Serology', 'Not enable', 
 'BHH038', 'HSV-IgG', 'NO', '-', 'Not Verified', '-'),

(532, 'Centromere Antibody', 7780676, 'Centromere Antibody', 2200, 0, 
 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 
 'BHC043', '-', 'NO', '-', 'Not Verified', '-'),

(530, 'Urine for Hemosiderin', 7780679, 'Urine for Hemosiderin', 550, 0, 
 'SELF', 'Urine', 'Specialise Outsource TT3', 'Clinical Pathology', 'Not enable', 
 'BHH019', '-', 'NO', '-', 'Not Verified', '-'),

(531, 'Complete Urine Analysis (CUE)', 7780678, 'Complete Urine Analysis (CUE)', 200, 0, 
 'SELF', 'Urine', 'Routine InHouse TT1', 'Clinical Pathology', 'Not enable', 
 NULL, 'URE ROUT', 'NO', '-', 'Not Verified', '-'),

(534, 'Stool for Occult Blood', 7780673, 'Stool for Occult Blood', 110, 0, 
 'SELF', 'Stool', 'Routine InHouse TT1', 'Clinical Pathology', 'Not enable', 
 'BHS025', '-', 'NO', '-', 'Not Verified', '-'),

(536, 'Protein - Random (Spot)', 7780671, 'Protein - Random (Spot)', 110, 0, 
 'SELF', 'Urine', 'Routine InHouse TT1', 'Clinical Pathology', 'Not enable', 
 'BHP062', 'PRO', 'NO', '-', 'Not Verified', '-'),

(541, 'Anti Sm-Antibody', 7780666, 'Anti Sm-Antibody', 1650, 0, 
 'SELF', 'Serum', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 
 'BHS015', '-', 'NO', '-', 'Not Verified', '-'),

(543, 'Bence Jones Protein (BJ Protein)', 7780664, 'Bence Jones Protein (BJ Protein)', 440, 0, 
 'SELF', 'Urine', 'Routine InHouse TT1', 'Clinical Pathology', 'Not enable', 
 'BHB002', 'BENCE JONES', 'NO', '-', 'Not Verified', '-'),

(551, 'Semen Analysis', 7780655, 'Semen Analysis', 550, 0, 
 'SELF', 'Semen', 'Routine InHouse TT1', 'Clinical Pathology', 'Not enable', 
 'BHS005', '-', 'NO', '-', 'Not Verified', '-'),

(554, 'Smear for Fungal Elements', 7780652, 'Smear for Fungal Elements', 330, 0, 
 'SELF', 'Any Specimen/Slides', '-', 'Clinical Microbiology', 'Not enable', 
 NULL, '-', 'NO', '-', 'Not Verified', '-'),

(561, 'KOH Preparation (Wet Mount)', 7780645, 'KOH Preparation (Wet Mount)', 440, 0, 
 'SELF', 'CSF', '-', 'Clinical Microbiology', 'Not enable', 
 NULL, '-', 'NO', '-', 'Not Verified', '-'),

(559, 'Metanephrines', 7780647, 'Metanephrines', 2090, 0, 
 'SELF', '24Hr Urine', 'Specialise Outsource TT3', 'Immunology & Serology', 'Not enable', 
 'BHM024', '-', 'NO', '-', 'Not Verified', '-'),

(562, 'Fungal Stain', 7780644, 'Fungal Stain', 330, 0, 
 'SELF', 'Body Fluid', '-', 'Clinical Microbiology', 'Not enable', 
 NULL, '-', 'NO', '-', 'Not Verified', '-'),

(575, 'Sputum For AFB', 7780630, 'Sputum For AFB', 330, 0, 
 'SELF', 'Any Specimen/Slides', '-', 'Clinical Microbiology', 'Not enable', 
 NULL, '-', 'NO', '-', 'Not Verified', '-'),

(584, 'Urine for Ketone Bodies', 7780620, 'Urine for Ketone Bodies', 605, 0, 
 'SELF', 'Urine', 'Routine InHouse TT1', 'Clinical Pathology', 'Not enable', 
 'BHA116', '-', 'NO', '-', 'Not Verified', '-');



