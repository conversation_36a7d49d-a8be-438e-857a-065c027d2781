#!/bin/bash

# CVBioLabs Production Deployment Script for DigitalOcean
# This script automates the deployment process

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="cvbiolabs"
DOMAIN_NAME="${1:-yourdomain.com}"
EMAIL="${2:-<EMAIL>}"

echo -e "${BLUE}🏥 CVBioLabs Production Deployment${NC}"
echo -e "${BLUE}====================================${NC}"
echo -e "Domain: ${DOMAIN_NAME}"
echo -e "Email: ${EMAIL}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p nginx/ssl
mkdir -p backups
mkdir -p logs
mkdir -p uploads/reports

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    print_warning ".env.production not found. Creating from template..."
    cp .env.production.template .env.production
    print_warning "Please edit .env.production with your actual configuration values"
    print_warning "Then run this script again"
    exit 1
fi

# Update nginx configuration with actual domain
print_status "Updating nginx configuration with domain: ${DOMAIN_NAME}"
sed -i "s/YOUR_DOMAIN.com/${DOMAIN_NAME}/g" nginx/sites-available/cvbiolabs.conf

# Build and start services
print_status "Building Docker images..."
docker-compose build

print_status "Starting services..."
docker-compose up -d db redis

# Wait for database to be ready
print_status "Waiting for database to be ready..."
sleep 30

# Start web application
print_status "Starting web application..."
docker-compose up -d web

# Install Certbot for SSL certificates
print_status "Setting up SSL certificates..."
if ! command -v certbot &> /dev/null; then
    print_warning "Certbot not found. Installing..."
    sudo apt-get update
    sudo apt-get install -y certbot python3-certbot-nginx
fi

# Generate SSL certificates
print_status "Generating SSL certificates for ${DOMAIN_NAME}..."
sudo certbot certonly --standalone --preferred-challenges http -d ${DOMAIN_NAME} -d www.${DOMAIN_NAME} --email ${EMAIL} --agree-tos --non-interactive

# Copy certificates to nginx directory
sudo cp /etc/letsencrypt/live/${DOMAIN_NAME}/fullchain.pem nginx/ssl/
sudo cp /etc/letsencrypt/live/${DOMAIN_NAME}/privkey.pem nginx/ssl/
sudo chown $(whoami):$(whoami) nginx/ssl/*.pem

# Start nginx
print_status "Starting nginx..."
docker-compose up -d nginx

# Setup automatic certificate renewal
print_status "Setting up automatic certificate renewal..."
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet && docker-compose restart nginx") | crontab -

# Setup database backup cron job
print_status "Setting up database backup..."
(crontab -l 2>/dev/null; echo "0 2 * * * cd $(pwd) && docker-compose run --rm backup") | crontab -

# Setup log rotation
print_status "Setting up log rotation..."
sudo tee /etc/logrotate.d/cvbiolabs > /dev/null <<EOF
$(pwd)/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 $(whoami) $(whoami)
    postrotate
        docker-compose restart web
    endscript
}
EOF

# Create monitoring script
print_status "Creating monitoring script..."
cat > monitor.sh << 'EOF'
#!/bin/bash
# Simple monitoring script for CVBioLabs

check_service() {
    if docker-compose ps $1 | grep -q "Up"; then
        echo "✅ $1 is running"
    else
        echo "❌ $1 is not running"
        docker-compose restart $1
    fi
}

echo "🏥 CVBioLabs Health Check - $(date)"
echo "=================================="

check_service web
check_service db
check_service redis
check_service nginx

# Check if website is responding
if curl -f -s http://localhost/health > /dev/null; then
    echo "✅ Website is responding"
else
    echo "❌ Website is not responding"
fi

echo ""
EOF

chmod +x monitor.sh

# Setup monitoring cron job
(crontab -l 2>/dev/null; echo "*/5 * * * * cd $(pwd) && ./monitor.sh >> logs/monitor.log 2>&1") | crontab -

print_status "Deployment completed successfully!"
echo ""
echo -e "${GREEN}🎉 CVBioLabs is now deployed!${NC}"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Point your domain ${DOMAIN_NAME} to this server's IP address"
echo "2. Edit .env.production with your actual configuration values"
echo "3. Restart services: docker-compose restart"
echo "4. Check logs: docker-compose logs -f"
echo "5. Monitor: ./monitor.sh"
echo ""
echo -e "${BLUE}Useful commands:${NC}"
echo "• View logs: docker-compose logs -f [service]"
echo "• Restart service: docker-compose restart [service]"
echo "• Backup database: docker-compose run --rm backup"
echo "• Update application: git pull && docker-compose build && docker-compose up -d"
echo ""
echo -e "${YELLOW}Security reminders:${NC}"
echo "• Change all default passwords in .env.production"
echo "• Setup firewall rules (UFW recommended)"
echo "• Regularly update system packages"
echo "• Monitor logs for suspicious activity"
