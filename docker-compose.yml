version: '3.8'

services:
  # Flask Application
  web:
    build: .
    container_name: cvbiolabs-web
    restart: unless-stopped
    environment:
      - FLASK_ENV=production
    env_file:
      - .env.production
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    networks:
      - cvbiolabs-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: cvbiolabs-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/sites-available:/etc/nginx/sites-available
      - ./nginx/ssl:/etc/nginx/ssl
      - ./static:/var/www/static
    depends_on:
      - web
    networks:
      - cvbiolabs-network

  # MySQL Database
  db:
    image: mysql:8.0
    container_name: cvbiolabs-db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database.sql:/docker-entrypoint-initdb.d/database.sql
    ports:
      - "3306:3306"
    networks:
      - cvbiolabs-network

  # Redis for Sessions and Rate Limiting
  redis:
    image: redis:7-alpine
    container_name: cvbiolabs-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - cvbiolabs-network

  # Backup Service
  backup:
    image: mysql:8.0
    container_name: cvbiolabs-backup
    restart: "no"
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
    volumes:
      - ./backups:/backups
      - mysql_data:/var/lib/mysql
    depends_on:
      - db
    networks:
      - cvbiolabs-network
    command: >
      sh -c "
        while ! mysqladmin ping -h db --silent; do
          echo 'Waiting for database connection...'
          sleep 2
        done
        echo 'Database is ready!'
        mysqldump -h db -u root -p${DB_ROOT_PASSWORD} ${DB_NAME} > /backups/cvbiolabs_backup_$$(date +%Y%m%d_%H%M%S).sql
        echo 'Backup completed!'
      "

volumes:
  mysql_data:
  redis_data:

networks:
  cvbiolabs-network:
    driver: bridge
