from flask import Flask, Blueprint, request, jsonify, send_file, render_template, redirect, url_for, session, flash, send_from_directory
from flask_cors import CORS
from flask_wtf import CSRFProtect
from werkzeug.utils import secure_filename
import os
import mysql.connector
from mysql.connector import Error
import traceback
from datetime import datetime, timedelta
import hashlib
import base64
from cryptography.fernet import Fernet
from functools import wraps
import bcrypt
# Removed Flask-Login imports
import requests
from dotenv import load_dotenv
from flask_wtf.csrf import generate_csrf
import logging
from logging.handlers import RotatingFileHandler
from flask_session import Session
import redis

# Load environment variables
load_dotenv()

# Configure logging
if not os.path.exists('logs'):
    os.makedirs('logs')

file_handler = RotatingFileHandler('logs/doctor.log', maxBytes=1024*1024, backupCount=10, encoding='utf-8')
file_handler.setFormatter(logging.Formatter(
    '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
))
file_handler.setLevel(logging.WARNING)

console_handler = logging.StreamHandler()
console_handler.setFormatter(logging.Formatter('%(levelname)s: %(message)s'))
console_handler.setLevel(logging.ERROR)

logger = logging.getLogger(__name__)
logger.setLevel(logging.WARNING)
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# Import secure file handling
from file_security import secure_file_handler

ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg', 'doc', 'docx'}

# Create Blueprint
doctor_bp = Blueprint('doctor', __name__, url_prefix='/doctor', template_folder='templates')

# Create Flask app
app = Flask(__name__)

# Secure environment variable enforcement
app.secret_key = os.getenv('SECRET_KEY')
if not app.secret_key:
    raise RuntimeError('SECRET_KEY must be set in .env for production!')

redis_url = os.getenv('REDIS_URL')
if not redis_url:
    raise RuntimeError('REDIS_URL must be set in .env for production!')
app.config['SESSION_REDIS'] = redis.from_url(redis_url)

# Database configuration (no insecure defaults)
DB_CONFIG = {
    'host': os.getenv('DB_HOST'),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'database': os.getenv('DB_NAME'),
    'charset': os.getenv('DB_CHARSET', 'utf8mb4')
}
for key in ['host', 'user', 'password', 'database']:
    if not DB_CONFIG[key]:
        raise RuntimeError(f"DB_{key.upper()} must be set in .env for production!")

# Configure Redis session
app.config['SESSION_TYPE'] = 'redis'
app.config['SESSION_COOKIE_SECURE'] = True
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=1)
Session(app)

# Initialize CSRF protection
csrf = CSRFProtect(app)

# Doctor class (no longer inherits UserMixin)
class Doctor:
    def __init__(self, doctor_data):
        self.id = doctor_data['id']
        self.email = doctor_data['email']
        self.name = doctor_data['name']

# Configuration
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

# Database connection helper
def get_db_connection():
    try:
        return mysql.connector.connect(
            host=os.getenv('DB_HOST'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD'),
            database=os.getenv('DB_NAME'),
            charset='utf8mb4'
        )
    except Error as e:
        logger.error(f"Error connecting to MySQL: {e}")
        raise

def generate_digital_signature(data):
    """Generate a digital signature for the given data using secure signature utility"""
    try:
        from security_utils import secure_signature
        return secure_signature.generate_signature(data)
    except Exception as e:
        logger.error(f"Signature generation failed: {str(e)}")
        raise

def verify_digital_signature(data, signature):
    """Verify the digital signature using secure signature utility"""
    try:
        from security_utils import secure_signature
        return secure_signature.verify_signature(data, signature)
    except Exception as e:
        logger.warning(f"Signature verification failed: {str(e)}")
        return False

def hash_password(password):
    """Hash a password for storing using secure authentication utilities."""
    try:
        from security_utils import secure_auth
        return secure_auth.hash_password(password)
    except Exception as e:
        logger.error(f"Password hashing failed: {str(e)}")
        raise

def check_password(password, password_hash):
    """Check hashed password using secure authentication utilities."""
    try:
        from security_utils import secure_auth
        return secure_auth.verify_password(password, password_hash)
    except Exception as e:
        logger.warning(f"Password check failed: {str(e)}")
        return False

def doctor_auth_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        doctor_id = session.get('doctor_id')
        if not doctor_id:
            return jsonify({'error': 'Unauthorized'}), 401
        return f(*args, **kwargs)
    return decorated

@doctor_bp.route('/')
@doctor_auth_required
def index():
    doctor_id = session.get('doctor_id')
    conn = get_db_connection()
    doctor = None
    try:
        with conn.cursor() as cursor:
            cursor.execute("SELECT name FROM doctors WHERE id = %s", (doctor_id,))
            doctor = cursor.fetchone()
    finally:
        conn.close()
    return render_template('doctor.html', doctor=doctor)

@doctor_bp.after_request
def after_request(response):
    # Generate CSRF token for every response
    response.headers['X-CSRF-Token'] = generate_csrf()
    return response

# Initialize Database Tables
def init_db():
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # Create admin_users table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS admin_users (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    professional_id VARCHAR(10) UNIQUE,
                    name VARCHAR(100),
                    email VARCHAR(100) UNIQUE,
                    password_hash VARCHAR(255),
                    role ENUM('Admin', 'Receptionist', 'Doctor') NOT NULL DEFAULT 'Admin',
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    phone VARCHAR(20),
                    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active'
                )
            """)
            # Create users table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    username VARCHAR(50) NOT NULL UNIQUE,
                    email VARCHAR(100) NOT NULL UNIQUE,
                    password_hash VARCHAR(255),
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    status TINYINT(1) DEFAULT 1,
                    otp_verified TINYINT(1) DEFAULT 0
                )
            """)
            # Create user_profiles table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_profiles (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    user_id INT NOT NULL UNIQUE,
                    first_name VARCHAR(60),
                    last_name VARCHAR(60),
                    phone VARCHAR(20),
                    date_of_birth DATE,
                    gender VARCHAR(10),
                    address TEXT,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                )
            """)
            # Create doctors table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS doctors (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    professional_id VARCHAR(10) UNIQUE,
                    name VARCHAR(100) NOT NULL,
                    specialization VARCHAR(100),
                    email VARCHAR(100) UNIQUE,
                    phone VARCHAR(20),
                    password_hash VARCHAR(255),
                    licence_number VARCHAR(50),
                    status ENUM('active', 'inactive') DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            """)
            # Create bookings table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS bookings (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    user_id INT NOT NULL,
                    test_id INT NOT NULL,
                    booking_date DATE NOT NULL,
                    appointment_time TIME NOT NULL DEFAULT '00:00:00',
                    booking_status ENUM('pending', 'confirmed', 'cancelled', 'completed', 'failed') NOT NULL DEFAULT 'pending',
                    barcode VARCHAR(255) NOT NULL UNIQUE,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    address_line1 VARCHAR(255) NOT NULL,
                    address_line2 VARCHAR(255),
                    city VARCHAR(100) NOT NULL,
                    state VARCHAR(100) NOT NULL,
                    postal_code VARCHAR(20) NOT NULL,
                    country VARCHAR(100) DEFAULT 'India',
                    latitude DECIMAL(10,8),
                    longitude DECIMAL(11,8),
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                )
            """)
            # Create coupons table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS coupons (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    code VARCHAR(50) NOT NULL UNIQUE,
                    discount_amount DECIMAL(10, 2) NOT NULL,
                    expiry_date DATE NOT NULL,
                    status ENUM('Active', 'Expired', 'Used') NOT NULL DEFAULT 'Active',
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            """)
            # Create coupon_usage table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS coupon_usage (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    coupon_id INT NOT NULL,
                    user_id INT NOT NULL,
                    booking_id INT,
                    used_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE SET NULL,
                    UNIQUE (coupon_id, user_id)
                )
            """)
            # Create otp_verification table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS otp_verification (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    phone VARCHAR(15) NOT NULL,
                    email VARCHAR(100) NOT NULL,
                    otp VARCHAR(6) NOT NULL,
                    verified TINYINT(1) NOT NULL DEFAULT 0,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL
                )
            """)
            # Create payments table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS payments (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    booking_id INT NOT NULL,
                    amount DECIMAL(10, 2) NOT NULL,
                    payment_method ENUM('RazorPay', 'UPI', 'Card', 'Net Banking', 'Cash') NOT NULL,
                    transaction_id VARCHAR(100) UNIQUE,
                    payment_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    payment_status VARCHAR(50) NOT NULL,
                    refund_amount DECIMAL(10, 2) DEFAULT 0,
                    refund_date TIMESTAMP NULL DEFAULT NULL,
                    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE
                )
            """)
            # Create pickup_agents table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS pickup_agents (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    professional_id VARCHAR(10) UNIQUE,
                    name VARCHAR(100) NOT NULL,
                    phone VARCHAR(15) NOT NULL UNIQUE,
                    status ENUM('Available', 'Busy', 'Inactive') NOT NULL DEFAULT 'Available',
                    vehicle_number VARCHAR(20),
                    service_area VARCHAR(100),
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    email VARCHAR(100) UNIQUE,
                    password_hash VARCHAR(255)
                )
            """)
            # Create reports table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS reports (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    booking_id INT NOT NULL,
                    patient_id INT NOT NULL,
                    test_id INT NOT NULL,
                    doctor_id INT,
                    assigned_doctor_id INT,
                    assign_status ENUM('Not Assigned', 'Assigned') NOT NULL DEFAULT 'Not Assigned',
                    report_url VARCHAR(255) NOT NULL,
                    report_status ENUM('Pending', 'Verified', 'Completed') NOT NULL DEFAULT 'Pending',
                    doctor_review TEXT,
                    comments TEXT,
                    report_type ENUM('standard', 'detailed', 'summary') DEFAULT 'standard',
                    report_format ENUM('pdf', 'excel', 'csv') DEFAULT 'pdf',
                    file_path VARCHAR(255),
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
                    FOREIGN KEY (assigned_doctor_id) REFERENCES doctors(id),
                    FOREIGN KEY (patient_id) REFERENCES users(id),
                    FOREIGN KEY (doctor_id) REFERENCES doctors(id)
                )
            """)
            # Create report_history table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS report_history (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    report_id INT NOT NULL,
                    action VARCHAR(50) NOT NULL,
                    doctor_id INT,
                    details TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (report_id) REFERENCES reports(id) ON DELETE CASCADE,
                    FOREIGN KEY (doctor_id) REFERENCES doctors(id)
                )
            """)
            # Create referrals table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS referrals (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    referrer_id INT NOT NULL,
                    referred_id INT NOT NULL,
                    referral_code VARCHAR(50) NOT NULL,
                    referral_status ENUM('Pending', 'Rewarded') NOT NULL DEFAULT 'Pending',
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE
                )
            """)
            # Create sample_collections table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sample_collections (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    booking_id INT NOT NULL,
                    agent_id INT NOT NULL,
                    collection_status ENUM('Pending', 'Collected', 'Delivered') NOT NULL DEFAULT 'Pending',
                    collection_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
                    FOREIGN KEY (agent_id) REFERENCES pickup_agents(id)
                )
            """)
            # Create testdetails table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS testdetails (
                    SrNo INT PRIMARY KEY,
                    TestName VARCHAR(255),
                    TestID BIGINT,
                    TestCode VARCHAR(255),
                    TestAmount DECIMAL(10, 2),
                    OutsourceAmount DECIMAL(10, 2),
                    OutsourceCenter VARCHAR(255),
                    SampleType VARCHAR(255),
                    TestCategory VARCHAR(255),
                    DepartmentName VARCHAR(255),
                    Accreditation VARCHAR(255),
                    IntegrationCode VARCHAR(255),
                    ShortText VARCHAR(255),
                    CAPTest VARCHAR(5),
                    TargetTAT VARCHAR(255),
                    VerificationStatus VARCHAR(255),
                    TargetTATHHMM VARCHAR(255),
                    active BOOLEAN DEFAULT TRUE
                )
            """)
            # Create patient_report table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS patient_report (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    report_id INT NOT NULL,
                    booking_id INT NOT NULL,
                    patient_id INT NOT NULL,
                    test_name VARCHAR(255) NOT NULL,
                    barcode VARCHAR(255) NOT NULL,
                    report_url VARCHAR(255) NOT NULL,
                    report_status ENUM('Pending', 'Verified', 'Completed') DEFAULT 'Pending',
                    comment TEXT,
                    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    sent_by_receptionist_id INT,
                    verified_by_admin_id INT,
                    FOREIGN KEY (report_id) REFERENCES reports(id) ON DELETE CASCADE,
                    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
                    FOREIGN KEY (patient_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (sent_by_receptionist_id) REFERENCES admin_users(id) ON DELETE SET NULL,
                    FOREIGN KEY (verified_by_admin_id) REFERENCES admin_users(id) ON DELETE SET NULL
                )
            """)
        conn.commit()
    except Exception as e:
        print("Database Initialization Error:", str(e))
    finally:
        conn.close()

# File validation (deprecated - use secure_file_handler instead)
def allowed_file(filename):
    """Legacy function - use secure_file_handler.validate_file_extension instead"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_safe_file_path(filename):
    """Legacy function - use secure_file_handler.secure_upload instead"""
    filename = secure_filename(filename)
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    return os.path.join(UPLOAD_FOLDER, f"{timestamp}_{filename}")

def verify_database_schema():
    """Verify that all required tables exist and have correct structure"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # Check if tables exist
            required_tables = ['reports', 'bookings', 'testdetails', 'users', 'user_profiles', 'doctors']
            for table in required_tables:
                cursor.execute(f"SHOW TABLES LIKE '{table}'")
                if not cursor.fetchone():
                    logger.error(f"Missing table: {table}")
                    return False
            return True
    except Exception as e:
        logger.error(f"Error verifying database schema: {str(e)}")
        return False
    finally:
        conn.close()

# Routes
@doctor_bp.route('/reports', methods=['GET'])
@doctor_auth_required
def get_reports():
    doctor_id = session.get('doctor_id')
    status = request.args.get('status', '').lower()
    conn = get_db_connection()
    try:
        with conn.cursor(dictionary=True) as cursor:
            base_query = """
                SELECT r.*, 
                       COALESCE(t.TestName, 'N/A') as test_name,
                       CONCAT(COALESCE(up.first_name, ''), ' ', COALESCE(up.last_name, '')) as patient_name,
                       b.barcode,
                       DATE_FORMAT(r.created_at, '%Y-%m-%d %H:%i') as formatted_date,
                       b.booking_date,
                       b.appointment_time,
                       COALESCE(t.TestCode, 'N/A') as test_code,
                       up.phone
                FROM reports r
                LEFT JOIN bookings b ON r.booking_id = b.id
                LEFT JOIN testdetails t ON r.test_id = t.SrNo
                LEFT JOIN users u ON r.patient_id = u.id
                LEFT JOIN user_profiles up ON u.id = up.user_id
                WHERE r.doctor_id = %s
            """
            params = [doctor_id]
            if status in ['pending', 'verified']:
                base_query += " AND r.report_status = %s"
                params.append(status.capitalize())
            base_query += " ORDER BY r.created_at DESC"
            cursor.execute(base_query, tuple(params))
            reports = cursor.fetchall()
            # ...existing code for formatting...
            for report in reports:
                if 'created_at' in report:
                    report['created_at'] = report['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                if 'updated_at' in report:
                    report['updated_at'] = report['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                if 'booking_date' in report:
                    report['booking_date'] = report['booking_date'].strftime('%Y-%m-%d')
                if 'appointment_time' in report:
                    report['appointment_time'] = str(report['appointment_time'])
            return jsonify(reports)
    except Exception as e:
        logger.error(f"Error fetching report: {str(e)}")
        return jsonify({'error': str(e)}), 500
    finally:
        if 'conn' in locals():
            conn.close()

@doctor_bp.route('/dashboard')
@doctor_auth_required
def doctor_dashboard():
    try:
        conn = get_db_connection()
        if not conn:
            flash('Database connection failed', 'error')
            return render_template('doctor.html', 
                                reports=[],
                                total_reports=0,
                                pending_reports=0,
                                verified_reports=0)

        doctor_id = session.get('doctor_id')
        if not doctor_id:
            flash('Doctor session not found', 'error')
            return redirect(url_for('doctor.logout'))

        with conn.cursor(dictionary=True) as cursor:
            # Get doctor details
            cursor.execute("""
                SELECT id, name, specialization, email, phone, professional_id
                FROM doctors 
                WHERE id = %s AND status = 'active'
            """, (doctor_id,))
            doctor = cursor.fetchone()
            
            if not doctor:
                flash('Doctor profile not found or inactive', 'error')
                return redirect(url_for('doctor.logout'))

            # Get total reports assigned to this doctor
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM reports 
                WHERE (assigned_doctor_id = %s OR doctor_id = %s)
            """, (doctor_id, doctor_id))
            result = cursor.fetchone()
            total_reports = result['count'] if result else 0

            # Get pending reports
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM reports 
                WHERE (assigned_doctor_id = %s OR doctor_id = %s)
                AND report_status = 'Pending'
            """, (doctor_id, doctor_id))
            result = cursor.fetchone()
            pending_reports = result['count'] if result else 0

            # Get verified reports
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM reports 
                WHERE (assigned_doctor_id = %s OR doctor_id = %s)
                AND report_status = 'Verified'
            """, (doctor_id, doctor_id))
            result = cursor.fetchone()
            verified_reports = result['count'] if result else 0

            # Get recent reports with patient and test details
            cursor.execute("""
                SELECT r.*, 
                       t.TestName,
                       CONCAT(up.first_name, ' ', up.last_name) as patient_name,
                       b.barcode,
                       DATE_FORMAT(r.created_at, '%Y-%m-%d %H:%i') as formatted_date
                FROM reports r
                JOIN bookings b ON r.booking_id = b.id
                JOIN testdetails t ON r.test_id = t.SrNo
                JOIN users u ON r.patient_id = u.id
                JOIN user_profiles up ON u.id = up.user_id
                WHERE (r.assigned_doctor_id = %s OR r.doctor_id = %s)
                ORDER BY r.created_at DESC
                LIMIT 10
            """, (doctor_id, doctor_id))
            reports = cursor.fetchall()

            logger.info(f"Found {len(reports)} reports for doctor {doctor_id}")  # Debug log

            return render_template('doctor.html',
                                reports=reports,
                                total_reports=total_reports,
                                pending_reports=pending_reports,
                                verified_reports=verified_reports,
                                doctor=doctor,
                                current_user={'name': doctor['name']})

    except Exception as e:
        print(f"Doctor dashboard error: {str(e)}")
        flash(f'Error loading dashboard: {str(e)}', 'error')
        return render_template('doctor.html',
                            reports=[],
                            total_reports=0,
                            pending_reports=0,
                            verified_reports=0,
                            doctor=None,
                            current_user={'name': session.get('doctor_name')})
    finally:
        if 'conn' in locals():
            conn.close()

@doctor_bp.route('/reports/upload', methods=['POST'])
def upload_report():
    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400

    file = request.files['file']
    booking_id = request.form.get('booking_id')
    patient_id = request.form.get('patient_id')
    test_id = request.form.get('test_id')

    if not booking_id or not patient_id or not test_id:
        return jsonify({'error': 'Missing required parameters'}), 400

    # Use secure file upload
    success, message, file_info = secure_file_handler.secure_upload(file, 'reports')

    if not success:
        return jsonify({'error': message}), 400

    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            cursor.execute("""
                INSERT INTO reports (booking_id, patient_id, test_id, report_url, report_status, file_path)
                VALUES (%s, %s, %s, %s, 'Pending', %s)
            """, (booking_id, patient_id, test_id, file_info['relative_path'], file_info['relative_path']))

        conn.commit()

        # Log successful upload
        logger.info(f"Report uploaded successfully: {file_info['secure_filename']} for booking {booking_id}")

        return jsonify({
            'message': 'Report uploaded successfully',
            'file_info': {
                'filename': file_info['secure_filename'],
                'size': file_info['file_size'],
                'type': file_info['file_type']
            }
        }), 201
        
    except Exception as e:
        logger.error(f"Error uploading report: {str(e)}")
        return jsonify({'error': 'Failed to upload report'}), 500
    finally:
        conn.close()

@doctor_bp.route('/reports/<int:report_id>/view', methods=['GET'])
def view_report(report_id):
    conn = get_db_connection()
    try:
        with conn.cursor(dictionary=True) as cursor:  # FIX: use dictionary=True
            cursor.execute("""
                SELECT r.*, b.barcode, b.booking_date, b.appointment_time,
                       t.TestName as test_name, t.TestCode as test_code,
                       u.username as patient_name,
                       up.first_name, up.last_name, up.phone
                FROM reports r
                JOIN bookings b ON r.booking_id = b.id
                JOIN testdetails t ON r.test_id = t.SrNo
                JOIN users u ON r.patient_id = u.id
                LEFT JOIN user_profiles up ON u.id = up.user_id
                WHERE r.id = %s
            """, (report_id,))
            report = cursor.fetchone()
            
            if not report:
                return jsonify({'error': 'Report not found'}), 404

            # Convert datetime objects to string format
            if report.get('booking_date'):
                report['booking_date'] = report['booking_date'].strftime('%Y-%m-%d')
            if report.get('appointment_time'):
                report['appointment_time'] = str(report['appointment_time'])
            if report.get('created_at'):
                report['created_at'] = report['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if report.get('updated_at'):
                report['updated_at'] = report['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                
            return jsonify(report)
    except Exception as e:
        logger.error(f"Error fetching report: {str(e)}")
        return jsonify({'error': 'Failed to fetch report'}), 500
    finally:
        conn.close()

@doctor_bp.route('/reports/<int:report_id>/verify', methods=['POST'])
@doctor_auth_required
def verify_report(report_id):
    doctor_id = session.get('doctor_id')
    logger.info(f"Doctor ID: {doctor_id}") # Debug log
    
    if not doctor_id:
        return jsonify({'error': 'Doctor not authenticated'}), 401

    try:
        data = request.get_json()
        comment = data.get('comment', '').strip()
        
        if not comment:
            return jsonify({'error': 'Verification comment is required'}), 400

        conn = get_db_connection()
        with conn.cursor(dictionary=True) as cursor:
            # Get current report status
            cursor.execute("""
                SELECT report_status, doctor_id 
                FROM reports 
                WHERE id = %s
            """, (report_id,))
            report = cursor.fetchone()
            
            if not report:
                return jsonify({'error': 'Report not found'}), 404

            logger.info(f"Current report status: {report['report_status']}") # Debug log
            
            # Verify the report belongs to this doctor
            if report['doctor_id'] != doctor_id:
                return jsonify({'error': 'Unauthorized to verify this report'}), 403

            # Update report status
            cursor.execute("""
                UPDATE reports 
                SET report_status = 'Verified',
                    doctor_review = %s,
                    updated_at = NOW()
                WHERE id = %s
            """, (comment, report_id))
            
            # Add to report history
            cursor.execute("""
                INSERT INTO report_history 
                (report_id, status, action, changed_by, doctor_id, comments, details)
                VALUES (%s, 'Verified', 'Verified', %s, %s, %s, %s)
            """, (report_id, None, doctor_id, comment, f'Report verified by doctor {doctor_id}'))
            
            conn.commit()
            print(f"Report {report_id} verified successfully")  # Debug log
            
            return jsonify({
                'message': 'Report verified successfully',
                'report_id': report_id
            })

    except Exception as e:
        logger.error(f"Error verifying report: {str(e)}")  # Debug log
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")  # Print full traceback
        if 'conn' in locals():
            conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        if 'conn' in locals():
            conn.close()

@doctor_bp.route('/uploads/reports/<path:filename>')
@doctor_bp.route('/uploads/<path:filename>')
def serve_report(filename):
    try:
        # Try to find the file in the uploads directory
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        if os.path.exists(file_path):
            return send_from_directory(UPLOAD_FOLDER, filename, as_attachment=False)
        
        # If not found, try without the 'reports/' prefix
        filename_without_prefix = os.path.basename(filename)
        file_path = os.path.join(UPLOAD_FOLDER, filename_without_prefix)
        if os.path.exists(file_path):
            return send_from_directory(UPLOAD_FOLDER, filename_without_prefix, as_attachment=False)
            
        logger.warning(f"File not found: {filename} or {filename_without_prefix}")
        return jsonify({'error': 'File not found'}), 404
    except Exception as e:
        logger.error(f"Error serving file {filename}: {str(e)}")
        return jsonify({'error': 'File not found'}), 404

@doctor_bp.route('/reports/<int:report_id>/file', methods=['GET'])
def get_report_file(report_id):
    conn = get_db_connection()
    try:
        with conn.cursor(dictionary=True) as cursor:  # FIX: use dictionary=True
            cursor.execute("SELECT report_url, file_path FROM reports WHERE id = %s", (report_id,))
            report = cursor.fetchone()
            
            if not report:
                return jsonify({'error': 'File not found'}), 404
            
            # Prefer file_path, fallback to report_url
            filename = report.get('file_path') or report.get('report_url')
            if not filename:
                return jsonify({'error': 'File not found'}), 404

            # Always serve from uploads/reports/
            reports_dir = os.path.join(UPLOAD_FOLDER, 'reports')
            abs_path = os.path.join(reports_dir, filename) if not os.path.isabs(filename) else filename
            if not os.path.exists(abs_path):
                # Try just the basename in case DB path is full or partial
                abs_path = os.path.join(reports_dir, os.path.basename(filename))
                if not os.path.exists(abs_path):
                    print(f"File not found: {abs_path}")
                    return jsonify({'error': 'File not found'}), 404
            try:
                return send_from_directory(reports_dir, os.path.basename(abs_path), as_attachment=True)
            except Exception as e:
                logger.error(f"Error sending file: {str(e)}")
                return jsonify({'error': 'Failed to send file'}), 500
    except Exception as e:
        logger.error(f"Error fetching file: {str(e)}")
        return jsonify({'error': 'Failed to retrieve file'}), 500
    finally:
        conn.close()

@doctor_bp.route('/reports/<int:report_id>', methods=['PUT'])
def edit_report(report_id):
    # Allow editing of verified reports (e.g., comments, doctor_review)
    data = request.json
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # Only allow editing if report is Verified
            cursor.execute("SELECT report_status FROM reports WHERE id = %s", (report_id,))
            report = cursor.fetchone()
            if not report:
                return jsonify({'error': 'Report not found'}), 404
            if report['report_status'] != 'Verified':
                return jsonify({'error': 'Only verified reports can be edited'}), 400
            # Update allowed fields
            update_fields = []
            params = []
            if 'doctor_review' in data:
                update_fields.append('doctor_review = %s')
                params.append(data['doctor_review'])
            if 'comments' in data:
                update_fields.append('comments = %s')
                params.append(data['comments'])
            if not update_fields:
                return jsonify({'error': 'No editable fields provided'}), 400
            params.append(report_id)
            cursor.execute(f"""
                UPDATE reports SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP WHERE id = %s
            """, params)
            # Add to report history
            cursor.execute("""
                INSERT INTO report_history (report_id, action, doctor_id, details)
                VALUES (%s, 'Edited', %s, %s)
            """, (report_id, 1, 'Report edited'))  # TODO: Use real doctor_id
            conn.commit()
            return jsonify({'message': 'Report updated successfully'})
    except Exception as e:
        logger.error(f"Error editing report: {str(e)}")
        conn.rollback()
        return jsonify({'error': 'Failed to update report'}), 500
    finally:
        conn.close()

@doctor_bp.route('/stats', methods=['GET'])
@doctor_auth_required
def get_stats():
    doctor_id = session.get('doctor_id')
    logger.info(f"Getting stats for doctor_id: {doctor_id}") # Debug log
    
    if not doctor_id:
        return jsonify({'error': 'Doctor not authenticated'}), 401

    conn = get_db_connection()
    try:
        with conn.cursor(dictionary=True) as cursor:
            # Get total reports for this doctor
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_reports,
                    SUM(CASE WHEN report_status = 'Pending' THEN 1 ELSE 0 END) as pending_reports,
                    SUM(CASE WHEN report_status = 'Verified' THEN 1 ELSE 0 END) as verified_reports
                FROM reports 
                WHERE doctor_id = %s
            """, (doctor_id,))
            
            stats = cursor.fetchone()
            logger.info(f"Stats found: {stats}") # Debug log
            
            if not stats:
                return jsonify({
                    'total_reports': 0,
                    'pending_reports': 0,
                    'verified_reports': 0
                })
            
            return jsonify({
                'total_reports': stats['total_reports'] or 0,
                'pending_reports': stats['pending_reports'] or 0,
                'verified_reports': stats['verified_reports'] or 0
            })

    except Exception as e:
        print(f"Error getting stats: {str(e)}")  # Debug log
        return jsonify({'error': str(e)}), 500
    finally:
        if 'conn' in locals():
            conn.close()

@doctor_bp.route('/doctors/profile', methods=['GET'])
@doctor_auth_required
def get_doctor_profile():
    doctor_id = session.get('doctor_id')
    if not doctor_id:
        return jsonify({'error': 'Doctor not authenticated'}), 401

    conn = get_db_connection()
    try:
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("""
                SELECT id, professional_id, name, specialization, email, phone, 
                       licence_number, status, created_at
                FROM doctors 
                WHERE id = %s
            """, (doctor_id,))
            doctor = cursor.fetchone()
            
            if not doctor:
                return jsonify({'error': 'Doctor not found'}), 404

            # Format dates
            if doctor['created_at']:
                doctor['created_at'] = doctor['created_at'].strftime('%Y-%m-%d %H:%M:%S')

            return jsonify(doctor)

    except Exception as e:
        print(f"Error fetching doctor profile: {str(e)}")
        return jsonify({'error': str(e)}), 500
    finally:
        if 'conn' in locals():
            conn.close()

@doctor_bp.route('/doctors/profile', methods=['PUT'])
@doctor_auth_required
def update_doctor_profile():
    doctor_id = session.get('doctor_id')
    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    name = data.get('name', '').strip()
    email = data.get('email', '').strip()
    specialization = data.get('specialization', '').strip()
    license_number = data.get('license_number', '').strip()
    if not name or not email or not specialization or not license_number:
        return jsonify({'error': 'All fields are required'}), 400
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            cursor.execute("""
                UPDATE doctors SET name=%s, email=%s, specialization=%s, licence_number=%s, updated_at=NOW()
                WHERE id=%s
            """, (name, email, specialization, license_number, doctor_id))
        conn.commit()
        return jsonify({'message': 'Profile updated successfully'})
    except Exception as e:
        logger.error(f"Error updating doctor profile: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
    finally:
        conn.close()

@doctor_bp.route('/logout')
def doctor_logout():
    # Clear doctor-specific session data
    session.pop('doctor_id', None)
    session.pop('doctor_name', None)
    session.pop('doctor_role', None)
    
    # Clear any flash messages
    flash('You have been logged out', 'success')
    
    return redirect(url_for('home'))

# Error handlers
@doctor_bp.errorhandler(401)
def unauthorized_error(error):
    if request.path.startswith('/api/'):
        return jsonify({'error': 'Unauthorized'}), 401
    return redirect(url_for('doctor.login_page'))

@doctor_bp.errorhandler(403)
def forbidden_error(error):
    if request.path.startswith('/api/'):
        return jsonify({'error': 'Forbidden'}), 403
    return redirect(url_for('doctor.login_page'))

@doctor_bp.errorhandler(404)
def not_found_error(error):
    if request.path.startswith('/api/'):
        return jsonify({'error': 'Not found'}), 404
    return redirect(url_for('doctor.login_page'))

@doctor_bp.errorhandler(400)
def bad_request_error(error):
    if request.path.startswith('/api/'):
        return jsonify({'error': 'Bad request'}), 400
    return render_template('error.html', error='Bad request'), 400