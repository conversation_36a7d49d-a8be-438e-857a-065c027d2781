"""
Professional Email Service for CVBioLabs
Handles all email communications with HTML templates and professional formatting
"""

import os
import logging
from datetime import datetime, timedelta
from flask import render_template, current_app
from flask_mail import Message, Mail
from typing import Dict, List, Optional, Any
import uuid
import base64

# Configure logging
logger = logging.getLogger(__name__)

class CVBioLabsEmailService:
    """Professional email service for CVBioLabs with HTML templates"""

    def __init__(self, mail_instance: Mail):
        self.mail = mail_instance
        self.company_name = "CVBioLabs"
        self.company_tagline = "Advanced Medical Diagnostics"
        self._logo_base64 = None

    @property
    def sender_email(self):
        """Get sender email from current app config"""
        from flask import current_app
        return current_app.config.get('MAIL_DEFAULT_SENDER')

    @property
    def logo_base64(self):
        """Get base64 encoded logo for email embedding"""
        if self._logo_base64 is None:
            self._load_logo_base64()
        return self._logo_base64

    def _load_logo_base64(self):
        """Load and cache the base64 encoded logo"""
        try:
            # Try to read from the generated base64 file first
            if os.path.exists('logo_base64.txt'):
                with open('logo_base64.txt', 'r') as f:
                    self._logo_base64 = f.read().strip()
                logger.info("Logo base64 loaded from logo_base64.txt")
                return

            # Fallback: convert CV.png to base64 on the fly
            logo_path = "static/images/CV.png"
            if os.path.exists(logo_path):
                with open(logo_path, "rb") as image_file:
                    encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
                    self._logo_base64 = f"data:image/png;base64,{encoded_string}"
                logger.info("Logo base64 generated from CV.png")
                return

            # If no logo found, set to None (will use text fallback)
            self._logo_base64 = None
            logger.warning("No logo file found, using text fallback")

        except Exception as e:
            logger.error(f"Error loading logo base64: {e}")
            self._logo_base64 = None
        
    def _get_base_context(self, recipient_email: str) -> Dict[str, Any]:
        """Get base context variables for all email templates"""
        from flask import request

        # Get base URL from current request or use default
        try:
            base_url = request.host_url.rstrip('/')
        except:
            base_url = 'https://cvbiolabs.com'

        return {
            'current_year': datetime.now().year,
            'recipient_email': recipient_email,
            'company_name': self.company_name,
            'company_tagline': self.company_tagline,
            'support_email': '<EMAIL>',
            'website_url': base_url,
            'dashboard_url': f'{base_url}/dashboard',
            'login_url': f'{base_url}/login',
            'packages_url': f'{base_url}/packages',
            'consultation_url': f'{base_url}/consultation',
            'reschedule_url': f'{base_url}/reschedule',
            'cancel_url': f'{base_url}/cancel',
            'logo_base64': self.logo_base64
        }
    
    def _send_email(self, 
                   recipient: str, 
                   subject: str, 
                   template_name: str, 
                   context: Dict[str, Any],
                   attachments: Optional[List[Dict]] = None) -> bool:
        """
        Send email using HTML template
        
        Args:
            recipient: Email address of recipient
            subject: Email subject line
            template_name: Name of the email template (without .html)
            context: Template context variables
            attachments: List of attachment dictionaries
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            # Merge with base context
            full_context = {**self._get_base_context(recipient), **context}
            
            # Render HTML content
            html_content = render_template(f'emails/{template_name}.html', **full_context)
            
            # Create message
            msg = Message(
                subject=f"{self.company_name} - {subject}",
                recipients=[recipient],
                html=html_content,
                sender=self.sender_email
            )
            
            # Add attachments if provided
            if attachments:
                for attachment in attachments:
                    msg.attach(
                        filename=attachment.get('filename'),
                        content_type=attachment.get('content_type'),
                        data=attachment.get('data')
                    )
            
            # Send email
            self.mail.send(msg)
            logger.info(f"Email sent successfully to {recipient}: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email to {recipient}: {str(e)}", exc_info=True)
            return False
    
    def send_otp_verification(self, 
                            recipient: str, 
                            user_name: str, 
                            otp: str,
                            verification_url: Optional[str] = None) -> bool:
        """Send OTP verification email"""
        context = {
            'user_name': user_name,
            'otp': otp,
            'verification_url': verification_url or f"{self._get_base_context(recipient)['website_url']}/verify-otp"
        }
        
        return self._send_email(
            recipient=recipient,
            subject="Email Verification Required",
            template_name="otp_verification",
            context=context
        )
    
    def send_password_reset(self,
                          recipient: str,
                          user_name: str,
                          otp: str,
                          reset_url: Optional[str] = None) -> bool:
        """Send password reset email with enhanced professional template"""
        from datetime import datetime
        import time

        current_time = datetime.now()
        context = {
            'user_name': user_name,
            'otp': otp,
            'reset_url': reset_url or f"{self._get_base_context(recipient)['website_url']}/reset_password",
            'current_datetime': current_time.strftime('%B %d, %Y at %I:%M %p'),
            'current_timestamp': str(int(time.time())),
            'request_id': f"PWD-{int(time.time())}-{hash(recipient) % 10000:04d}"
        }

        return self._send_email(
            recipient=recipient,
            subject="CVBioLabs Password Reset Request - Secure Access Code Inside",
            template_name="password_reset",
            context=context
        )
    
    def send_payment_confirmation(self,
                                recipient: str,
                                customer_name: str,
                                amount: float,
                                transaction_id: str,
                                payment_method: str = "Online",
                                tests: Optional[List[Dict]] = None,
                                payment_date: Optional[datetime] = None) -> bool:
        """Send payment confirmation email"""
        context = {
            'customer_name': customer_name,
            'customer_email': recipient,
            'amount': amount,
            'transaction_id': transaction_id,
            'payment_method': payment_method,
            'tests': tests or [],
            'payment_date': payment_date or datetime.now(),
            'download_receipt_url': f"{self._get_base_context(recipient)['website_url']}/receipt/{transaction_id}"
        }

        return self._send_email(
            recipient=recipient,
            subject="Payment Confirmation",
            template_name="payment_confirmation",
            context=context
        )

    def send_invoice_notification(self,
                                recipient: str,
                                customer_name: str,
                                invoice_number: str,
                                total_amount: float,
                                tests: List[Dict],
                                invoice_date: datetime,
                                payment_status: str = "pending",
                                payment_method: Optional[str] = None,
                                transaction_id: Optional[str] = None,
                                payment_date: Optional[datetime] = None,
                                booking_id: Optional[str] = None,
                                due_date: Optional[datetime] = None,
                                subtotal: Optional[float] = None,
                                tax_amount: Optional[float] = None) -> bool:
        """Send invoice ready notification email"""
        base_context = self._get_base_context(recipient)

        context = {
            'customer_name': customer_name,
            'invoice_number': invoice_number,
            'total_amount': total_amount,
            'tests': tests,
            'invoice_date': invoice_date,
            'payment_status': payment_status,
            'payment_method': payment_method,
            'transaction_id': transaction_id,
            'payment_date': payment_date,
            'booking_id': booking_id,
            'due_date': due_date or (invoice_date + timedelta(days=30)),
            'subtotal': subtotal or total_amount,
            'tax_amount': tax_amount or 0,
            'invoice_download_url': f"{base_context['website_url']}/download_invoice/{invoice_number}"
        }

        return self._send_email(
            recipient=recipient,
            subject=f"Invoice #{invoice_number} Ready - CVBioLabs",
            template_name="invoice_ready",
            context=context
        )
    
    def send_payment_link(self, 
                         recipient: str, 
                         customer_name: str, 
                         amount: float,
                         payment_url: str,
                         expiry_date: datetime,
                         request_id: str,
                         description: Optional[str] = None) -> bool:
        """Send payment link email"""
        context = {
            'customer_name': customer_name,
            'amount': amount,
            'payment_url': payment_url,
            'expiry_date': expiry_date,
            'request_id': request_id,
            'description': description
        }
        
        return self._send_email(
            recipient=recipient,
            subject="Payment Request",
            template_name="payment_link",
            context=context
        )
    
    def send_admin_password_reset(self, 
                                recipient: str, 
                                user_name: str, 
                                user_type: str,
                                professional_id: str,
                                new_password: str,
                                reset_date: Optional[datetime] = None) -> bool:
        """Send admin password reset notification"""
        context = {
            'user_name': user_name,
            'user_type': user_type,
            'user_email': recipient,
            'professional_id': professional_id,
            'new_password': new_password,
            'reset_date': reset_date or datetime.now()
        }
        
        return self._send_email(
            recipient=recipient,
            subject="Password Reset Notification",
            template_name="admin_password_reset",
            context=context
        )
    
    def send_welcome_email(self,
                          recipient: str,
                          user_name: str,
                          user_id: str) -> bool:
        """Send welcome email to new users"""
        context = {
            'user_name': user_name,
            'user_email': recipient,
            'user_id': user_id
        }

        return self._send_email(
            recipient=recipient,
            subject="Welcome to CVBioLabs!",
            template_name="welcome",
            context=context
        )

    def send_admin_user_welcome_email(self,
                                    recipient: str,
                                    user_name: str,
                                    professional_id: str,
                                    user_role: str,
                                    password: Optional[str] = None) -> bool:
        """Send professional welcome email to admin-created users"""
        base_context = self._get_base_context(recipient)

        # Determine login URL based on user role
        if user_role == 'Pickup Agent':
            login_url = f"{base_context['website_url']}/agent/login"
        elif user_role == 'Doctor':
            login_url = f"{base_context['website_url']}/doctor/login"
        else:
            login_url = f"{base_context['website_url']}/admin/login"

        context = {
            'user_name': user_name,
            'user_email': recipient,
            'professional_id': professional_id,
            'user_role': user_role,
            'password': password,
            'login_url': login_url
        }

        # Create role-specific subject
        if user_role == 'Doctor':
            subject = f"Welcome Dr. {user_name} - Your CVBioLabs Medical Account is Ready"
        elif user_role == 'Pickup Agent':
            subject = f"Welcome {user_name} - Your CVBioLabs Agent Account is Ready"
        else:
            subject = f"Welcome {user_name} - Your CVBioLabs {user_role} Account is Ready"

        return self._send_email(
            recipient=recipient,
            subject=subject,
            template_name="admin_welcome",
            context=context
        )
    
    def send_test_report_ready(self, 
                              recipient: str, 
                              patient_name: str, 
                              test_names: List[str],
                              report_url: str,
                              collection_date: datetime) -> bool:
        """Send test report ready notification"""
        context = {
            'patient_name': patient_name,
            'test_names': test_names,
            'report_url': report_url,
            'collection_date': collection_date,
            'test_count': len(test_names)
        }
        
        return self._send_email(
            recipient=recipient,
            subject="Your Test Reports Are Ready",
            template_name="test_report_ready",
            context=context
        )
    
    def send_appointment_confirmation(self, 
                                    recipient: str, 
                                    patient_name: str, 
                                    appointment_date: datetime,
                                    appointment_type: str,
                                    tests: List[str],
                                    address: str) -> bool:
        """Send appointment confirmation email"""
        context = {
            'patient_name': patient_name,
            'appointment_date': appointment_date,
            'appointment_type': appointment_type,
            'tests': tests,
            'address': address
        }
        
        return self._send_email(
            recipient=recipient,
            subject="Appointment Confirmation",
            template_name="appointment_confirmation",
            context=context
        )
    
    def send_security_alert(self,
                           recipient: str,
                           alert_type: str,
                           alert_message: str,
                           alert_details: Dict[str, Any],
                           timestamp: Optional[datetime] = None) -> bool:
        """Send security alert email"""
        context = {
            'alert_type': alert_type,
            'alert_message': alert_message,
            'alert_details': alert_details,
            'timestamp': timestamp or datetime.now()
        }

        return self._send_email(
            recipient=recipient,
            subject=f"Security Alert: {alert_type}",
            template_name="security_alert",
            context=context
        )

    def send_subscription_confirmation(self,
                                     recipient: str,
                                     unsubscribe_token: Optional[str] = None) -> bool:
        """Send newsletter subscription confirmation email"""
        base_context = self._get_base_context(recipient)

        context = {
            'unsubscribe_url': f"{base_context['website_url']}/unsubscribe?token={unsubscribe_token}" if unsubscribe_token else "#",
            'packages_url': f"{base_context['website_url']}/test",
            'support_email': base_context['support_email'],
            'company_tagline': "Advanced Medical Diagnostics for a Healthy Life",
            'logo_base64': None  # Don't use logo for newsletter emails, use text fallback
        }

        return self._send_email(
            recipient=recipient,
            subject="Welcome to CVBioLabs - Subscription Confirmed!",
            template_name="subscription_confirmation",
            context=context
        )

# Global email service instance
email_service = None

def init_email_service(mail_instance: Mail) -> CVBioLabsEmailService:
    """Initialize the email service"""
    global email_service
    email_service = CVBioLabsEmailService(mail_instance)
    return email_service

def get_email_service() -> CVBioLabsEmailService:
    """Get the email service instance"""
    global email_service
    if email_service is None:
        raise RuntimeError("Email service not initialized. Call init_email_service() first.")
    return email_service

# Convenience functions for backward compatibility
def send_otp_email(email: str, otp: str, user_name: str = None) -> bool:
    """Backward compatible OTP email function"""
    try:
        service = get_email_service()
        return service.send_otp_verification(
            recipient=email,
            user_name=user_name or "Valued Customer",
            otp=otp
        )
    except Exception as e:
        logger.error(f"Error sending OTP email: {e}", exc_info=True)
        return False

def send_password_reset_email(user_data: Dict, professional_id: str, user_type: str, password: str) -> bool:
    """Backward compatible password reset email function"""
    try:
        service = get_email_service()
        return service.send_admin_password_reset(
            recipient=user_data['email'],
            user_name=user_data['name'],
            user_type=user_type,
            professional_id=professional_id,
            new_password=password
        )
    except Exception as e:
        logger.error(f"Error sending password reset email: {e}", exc_info=True)
        return False
