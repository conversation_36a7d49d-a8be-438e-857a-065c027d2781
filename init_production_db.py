#!/usr/bin/env python3
"""
Production database initialization script for CVBioLabs
This script sets up the database with proper production configurations
"""

import os
import sys
import mysql.connector
from mysql.connector import Error
from dotenv import load_dotenv
import bcrypt
import secrets
import string

# Load environment variables
load_dotenv('.env.production')

def generate_secure_password(length=12):
    """Generate a secure random password"""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    password = ''.join(secrets.choice(alphabet) for i in range(length))
    return password

def hash_password(password):
    """Hash password using bcrypt"""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def create_database_connection():
    """Create database connection"""
    try:
        connection = mysql.connector.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD'),
            database=os.getenv('DB_NAME'),
            charset=os.getenv('DB_CHARSET', 'utf8mb4')
        )
        return connection
    except Error as e:
        print(f"❌ Error connecting to database: {e}")
        return None

def initialize_admin_user():
    """Initialize default admin user for production"""
    connection = create_database_connection()
    if not connection:
        return False
    
    try:
        cursor = connection.cursor()
        
        # Check if admin user already exists
        cursor.execute("SELECT COUNT(*) FROM admin_users WHERE role = 'Admin'")
        admin_count = cursor.fetchone()[0]
        
        if admin_count > 0:
            print("✅ Admin user already exists")
            return True
        
        # Create default admin user
        admin_password = generate_secure_password(16)
        admin_email = input("Enter admin email address: ").strip()
        admin_name = input("Enter admin name: ").strip()
        
        if not admin_email or not admin_name:
            print("❌ Email and name are required")
            return False
        
        hashed_password = hash_password(admin_password)
        professional_id = "ADMIN001"
        
        insert_query = """
        INSERT INTO admin_users (professional_id, name, email, password_hash, role, status)
        VALUES (%s, %s, %s, %s, 'Admin', 'active')
        """
        
        cursor.execute(insert_query, (professional_id, admin_name, admin_email, hashed_password))
        connection.commit()
        
        print("✅ Admin user created successfully!")
        print(f"📧 Email: {admin_email}")
        print(f"🔑 Password: {admin_password}")
        print("⚠️  IMPORTANT: Save this password securely and change it after first login!")
        
        return True
        
    except Error as e:
        print(f"❌ Error creating admin user: {e}")
        return False
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def optimize_database():
    """Apply production database optimizations"""
    connection = create_database_connection()
    if not connection:
        return False
    
    try:
        cursor = connection.cursor()
        
        # Production optimizations
        optimizations = [
            "SET GLOBAL innodb_buffer_pool_size = 1073741824;",  # 1GB
            "SET GLOBAL max_connections = 200;",
            "SET GLOBAL innodb_log_file_size = 268435456;",  # 256MB
            "SET GLOBAL query_cache_size = 67108864;",  # 64MB
            "SET GLOBAL slow_query_log = 1;",
            "SET GLOBAL long_query_time = 2;",
        ]
        
        for optimization in optimizations:
            try:
                cursor.execute(optimization)
                print(f"✅ Applied: {optimization}")
            except Error as e:
                print(f"⚠️  Could not apply {optimization}: {e}")
        
        connection.commit()
        return True
        
    except Error as e:
        print(f"❌ Error optimizing database: {e}")
        return False
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def verify_database_setup():
    """Verify database setup and tables"""
    connection = create_database_connection()
    if not connection:
        return False
    
    try:
        cursor = connection.cursor()
        
        # Check if main tables exist
        required_tables = [
            'admin_users', 'users', 'appointments', 'test_packages',
            'orders', 'payments', 'newsletters', 'audit_logs'
        ]
        
        cursor.execute("SHOW TABLES")
        existing_tables = [table[0] for table in cursor.fetchall()]
        
        missing_tables = [table for table in required_tables if table not in existing_tables]
        
        if missing_tables:
            print(f"⚠️  Missing tables: {', '.join(missing_tables)}")
            print("💡 Run the database.sql script to create missing tables")
            return False
        
        print("✅ All required tables exist")
        
        # Check table counts
        for table in required_tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"📊 {table}: {count} records")
        
        return True
        
    except Error as e:
        print(f"❌ Error verifying database: {e}")
        return False
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def main():
    """Main initialization function"""
    print("🏥 CVBioLabs Production Database Initialization")
    print("=" * 50)
    
    # Check environment variables
    required_vars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        print("💡 Please check your .env.production file")
        sys.exit(1)
    
    print("✅ Environment variables loaded")
    
    # Verify database setup
    print("\n1️⃣  Verifying database setup...")
    if not verify_database_setup():
        print("❌ Database verification failed")
        sys.exit(1)
    
    # Initialize admin user
    print("\n2️⃣  Setting up admin user...")
    if not initialize_admin_user():
        print("❌ Admin user setup failed")
        sys.exit(1)
    
    # Optimize database
    print("\n3️⃣  Applying database optimizations...")
    if not optimize_database():
        print("⚠️  Some optimizations failed, but continuing...")
    
    print("\n✅ Production database initialization completed!")
    print("\n🔐 Security reminders:")
    print("• Change the admin password after first login")
    print("• Regularly backup your database")
    print("• Monitor database performance")
    print("• Keep database software updated")

if __name__ == '__main__':
    main()
