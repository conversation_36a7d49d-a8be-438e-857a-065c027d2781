"""
CVBioLabs Invoice Generation Module
Generates professional PDF invoices for test bookings with consistent branding
"""

import io
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from decimal import Decimal

from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import (
    SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, 
    PageBreak, Image, KeepTogether
)
from reportlab.platypus.frames import Frame
from reportlab.platypus.doctemplate import PageTemplate, BaseDocTemplate
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT, TA_JUSTIFY

# Set up logging
logger = logging.getLogger(__name__)

class CVBioLabsInvoiceGenerator:
    """Professional invoice generator for CVBioLabs"""
    
    def __init__(self):
        self.company_name = "CVBioLabs"
        self.company_tagline = "For a Healthy Life - Advanced Medical Diagnostics"
        self.company_address = [
            "H.No-1624, Old MIG, Phase-II, BHEL",
            "Seri Lingampally, Hyderabad-500032",
            "Telangana, India"
        ]
        self.company_contact = {
            'phone': '+91 78936 20683',
            'email': '<EMAIL>',
            'website': 'www.cvbiolabs.com'
        }
        self.tax_info = {
            'gstin': 'GST123456789',  # Replace with actual GSTIN
            'pan': '**********'      # Replace with actual PAN
        }
        
        # CVBioLabs brand colors
        self.colors = {
            'primary_blue': colors.Color(0, 47/255, 108/255),      # #002f6c
            'primary_orange': colors.Color(244/255, 124/255, 32/255), # #f47c20
            'light_blue': colors.Color(230/255, 247/255, 255/255), # #e6f7ff
            'light_gray': colors.Color(248/255, 249/255, 250/255), # #f8f9fa
            'text_dark': colors.Color(51/255, 51/255, 51/255),     # #333333
            'text_light': colors.Color(102/255, 102/255, 102/255), # #666666
            'success_green': colors.Color(40/255, 167/255, 69/255), # #28a745
            'white': colors.white,
            'black': colors.black
        }
        
    def _get_logo_path(self) -> Optional[str]:
        """Get the path to the company logo"""
        logo_path = "static/images/CV.png"
        if os.path.exists(logo_path):
            return logo_path
        return None
    
    def _create_styles(self):
        """Create custom paragraph styles for the invoice"""
        styles = getSampleStyleSheet()
        
        # Company name style
        styles.add(ParagraphStyle(
            name='CompanyName',
            parent=styles['Heading1'],
            fontSize=24,
            textColor=self.colors['primary_blue'],
            alignment=TA_CENTER,
            spaceAfter=6,
            fontName='Helvetica-Bold'
        ))
        
        # Company tagline style
        styles.add(ParagraphStyle(
            name='CompanyTagline',
            parent=styles['Normal'],
            fontSize=12,
            textColor=self.colors['primary_orange'],
            alignment=TA_CENTER,
            spaceAfter=20,
            fontName='Helvetica-Oblique'
        ))
        
        # Invoice title style
        styles.add(ParagraphStyle(
            name='InvoiceTitle',
            parent=styles['Heading1'],
            fontSize=20,
            textColor=self.colors['primary_blue'],
            alignment=TA_CENTER,
            spaceAfter=20,
            fontName='Helvetica-Bold'
        ))
        
        # Section header style
        styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=styles['Heading2'],
            fontSize=14,
            textColor=self.colors['primary_blue'],
            alignment=TA_LEFT,
            spaceAfter=10,
            fontName='Helvetica-Bold'
        ))
        
        # Address style
        styles.add(ParagraphStyle(
            name='Address',
            parent=styles['Normal'],
            fontSize=10,
            textColor=self.colors['text_dark'],
            alignment=TA_LEFT,
            spaceAfter=4,
            fontName='Helvetica'
        ))
        
        # Invoice info style
        styles.add(ParagraphStyle(
            name='InvoiceInfo',
            parent=styles['Normal'],
            fontSize=11,
            textColor=self.colors['text_dark'],
            alignment=TA_LEFT,
            spaceAfter=4,
            fontName='Helvetica'
        ))
        
        # Footer style
        styles.add(ParagraphStyle(
            name='Footer',
            parent=styles['Normal'],
            fontSize=9,
            textColor=self.colors['text_light'],
            alignment=TA_CENTER,
            spaceAfter=4,
            fontName='Helvetica'
        ))
        
        return styles
    
    def _create_header(self, elements: List, styles: Dict) -> None:
        """Create the invoice header with logo and company information"""
        # Add logo if available
        logo_path = self._get_logo_path()
        if logo_path:
            try:
                logo = Image(logo_path, width=2*inch, height=0.8*inch)
                logo.hAlign = 'CENTER'
                elements.append(logo)
                elements.append(Spacer(1, 10))
            except Exception as e:
                logger.warning(f"Could not load logo: {e}")
                # Fallback to text logo
                elements.append(Paragraph(self.company_name, styles['CompanyName']))
        else:
            # Text-based logo
            elements.append(Paragraph(self.company_name, styles['CompanyName']))
        
        # Company tagline
        elements.append(Paragraph(self.company_tagline, styles['CompanyTagline']))
        
        # Orange accent line
        elements.append(Spacer(1, 10))
        line_table = Table([['']]) 
        line_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), self.colors['primary_orange']),
            ('LINEBELOW', (0, 0), (-1, -1), 3, self.colors['primary_orange']),
        ]))
        elements.append(line_table)
        elements.append(Spacer(1, 20))
    
    def _create_invoice_info_section(self, elements: List, styles: Dict, invoice_data: Dict) -> None:
        """Create the invoice information section"""
        # Invoice title
        elements.append(Paragraph("PAYMENT INVOICE", styles['InvoiceTitle']))
        elements.append(Spacer(1, 20))
        
        # Create two-column layout for company and customer info
        company_info = []
        company_info.append(f"<b>{self.company_name}</b>")
        for line in self.company_address:
            company_info.append(line)
        company_info.append(f"Phone: {self.company_contact['phone']}")
        company_info.append(f"Email: {self.company_contact['email']}")
        company_info.append(f"Website: {self.company_contact['website']}")
        if self.tax_info.get('gstin'):
            company_info.append(f"GSTIN: {self.tax_info['gstin']}")
        
        customer_info = []
        customer_info.append(f"<b>Bill To:</b>")
        customer_info.append(f"{invoice_data.get('customer_name', 'N/A')}")
        customer_info.append(f"Email: {invoice_data.get('customer_email', 'N/A')}")
        if invoice_data.get('customer_phone'):
            customer_info.append(f"Phone: {invoice_data['customer_phone']}")
        
        # Customer address
        address_parts = []
        if invoice_data.get('address_line1'):
            address_parts.append(invoice_data['address_line1'])
        if invoice_data.get('address_line2'):
            address_parts.append(invoice_data['address_line2'])
        if invoice_data.get('city'):
            address_parts.append(invoice_data['city'])
        if invoice_data.get('state'):
            address_parts.append(invoice_data['state'])
        if invoice_data.get('postal_code'):
            address_parts.append(invoice_data['postal_code'])
        if invoice_data.get('country'):
            address_parts.append(invoice_data['country'])
        
        for addr_line in address_parts:
            customer_info.append(addr_line)
        
        # Invoice details
        invoice_details = []
        invoice_details.append(f"<b>Invoice Details:</b>")
        invoice_details.append(f"Invoice No: {invoice_data.get('invoice_number', 'N/A')}")
        invoice_details.append(f"Invoice Date: {invoice_data.get('invoice_date', datetime.now()).strftime('%B %d, %Y')}")
        invoice_details.append(f"Due Date: {invoice_data.get('due_date', datetime.now() + timedelta(days=30)).strftime('%B %d, %Y')}")
        if invoice_data.get('booking_id'):
            invoice_details.append(f"Booking ID: {invoice_data['booking_id']}")
        if invoice_data.get('transaction_id'):
            invoice_details.append(f"Transaction ID: {invoice_data['transaction_id']}")
        
        # Create table for layout
        info_data = [
            [
                Paragraph('<br/>'.join(company_info), styles['Address']),
                Paragraph('<br/>'.join(customer_info), styles['Address']),
                Paragraph('<br/>'.join(invoice_details), styles['InvoiceInfo'])
            ]
        ]
        
        info_table = Table(info_data, colWidths=[2.2*inch, 2.2*inch, 2.2*inch])
        info_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 0),
        ]))
        
        elements.append(info_table)
        elements.append(Spacer(1, 30))

    def _create_test_items_table(self, elements: List, styles: Dict, invoice_data: Dict) -> None:
        """Create the test items table"""
        elements.append(Paragraph("Test Details", styles['SectionHeader']))
        elements.append(Spacer(1, 10))

        # Table headers
        headers = ['S.No', 'Test Name', 'Test Code', 'Quantity', 'Unit Price (₹)', 'Amount (₹)']
        table_data = [headers]

        # Add test items
        tests = invoice_data.get('tests', [])
        subtotal = Decimal('0.00')

        for i, test in enumerate(tests, 1):
            test_name = test.get('name', 'N/A')
            test_code = test.get('code', 'N/A')
            quantity = test.get('quantity', 1)
            unit_price = Decimal(str(test.get('amount', 0)))
            amount = unit_price * quantity
            subtotal += amount

            table_data.append([
                str(i),
                test_name,
                test_code,
                str(quantity),
                f"₹{unit_price:.2f}",
                f"₹{amount:.2f}"
            ])

        # Create table
        test_table = Table(table_data, colWidths=[0.5*inch, 2.5*inch, 1*inch, 0.8*inch, 1*inch, 1*inch])

        # Style the table
        test_table.setStyle(TableStyle([
            # Header styling
            ('BACKGROUND', (0, 0), (-1, 0), self.colors['primary_blue']),
            ('TEXTCOLOR', (0, 0), (-1, 0), self.colors['white']),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('VALIGN', (0, 0), (-1, 0), 'MIDDLE'),

            # Data rows styling
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('ALIGN', (0, 1), (0, -1), 'CENTER'),  # S.No column
            ('ALIGN', (1, 1), (2, -1), 'LEFT'),    # Test name and code
            ('ALIGN', (3, 1), (-1, -1), 'RIGHT'),  # Quantity, price, amount
            ('VALIGN', (0, 1), (-1, -1), 'MIDDLE'),

            # Alternating row colors
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [self.colors['white'], self.colors['light_gray']]),

            # Grid lines
            ('GRID', (0, 0), (-1, -1), 1, self.colors['text_light']),
            ('LINEBELOW', (0, 0), (-1, 0), 2, self.colors['primary_blue']),

            # Padding
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))

        elements.append(test_table)
        elements.append(Spacer(1, 20))

        # Calculate totals
        tax_rate = invoice_data.get('tax_rate', 0)  # Tax rate as percentage
        tax_amount = subtotal * Decimal(str(tax_rate)) / 100
        total_amount = subtotal + tax_amount

        # Store calculated amounts in invoice_data for later use
        invoice_data['subtotal'] = subtotal
        invoice_data['tax_amount'] = tax_amount
        invoice_data['total_amount'] = total_amount

        # Create totals table
        totals_data = [
            ['', '', '', '', 'Subtotal:', f"₹{subtotal:.2f}"]
        ]

        if tax_rate > 0:
            totals_data.append(['', '', '', '', f'Tax ({tax_rate}%):', f"₹{tax_amount:.2f}"])

        totals_data.append(['', '', '', '', 'Total Amount:', f"₹{total_amount:.2f}"])

        totals_table = Table(totals_data, colWidths=[0.5*inch, 2.5*inch, 1*inch, 0.8*inch, 1*inch, 1*inch])
        totals_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (4, 0), (-1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LINEABOVE', (4, 0), (-1, 0), 1, self.colors['text_light']),
            ('LINEABOVE', (4, -1), (-1, -1), 2, self.colors['primary_blue']),
            ('TEXTCOLOR', (4, -1), (-1, -1), self.colors['primary_blue']),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 4),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
        ]))

        elements.append(totals_table)
        elements.append(Spacer(1, 30))

    def _create_payment_info_section(self, elements: List, styles: Dict, invoice_data: Dict) -> None:
        """Create payment information section"""
        elements.append(Paragraph("Payment Information", styles['SectionHeader']))
        elements.append(Spacer(1, 10))

        payment_info = []
        payment_status = invoice_data.get('payment_status', 'pending').title()
        payment_method = invoice_data.get('payment_method', 'N/A')

        payment_info.append(f"<b>Payment Status:</b> {payment_status}")
        payment_info.append(f"<b>Payment Method:</b> {payment_method}")

        if invoice_data.get('transaction_id'):
            payment_info.append(f"<b>Transaction ID:</b> {invoice_data['transaction_id']}")

        if invoice_data.get('payment_date'):
            payment_date = invoice_data['payment_date']
            if isinstance(payment_date, str):
                payment_date = datetime.fromisoformat(payment_date.replace('Z', '+00:00'))
            payment_info.append(f"<b>Payment Date:</b> {payment_date.strftime('%B %d, %Y at %I:%M %p')}")

        # Payment terms
        payment_terms = invoice_data.get('payment_terms', [
            "Payment is due within 30 days of invoice date.",
            "Late payments may incur additional charges.",
            "For any payment queries, please contact our support team."
        ])

        payment_info.append("<br/><b>Payment Terms:</b>")
        for term in payment_terms:
            payment_info.append(f"• {term}")

        elements.append(Paragraph('<br/>'.join(payment_info), styles['InvoiceInfo']))
        elements.append(Spacer(1, 30))

    def _create_footer(self, elements: List, styles: Dict, invoice_data: Dict) -> None:
        """Create the invoice footer"""
        # Thank you message
        thank_you_msg = """
        <b>Thank you for choosing CVBioLabs!</b><br/>
        We appreciate your trust in our advanced medical diagnostic services.
        Our team is committed to providing you with accurate results and exceptional healthcare support.
        """
        elements.append(Paragraph(thank_you_msg, styles['InvoiceInfo']))
        elements.append(Spacer(1, 20))

        # Contact information
        contact_info = f"""
        <b>Need Help?</b><br/>
        📞 Phone: {self.company_contact['phone']} (Available 24/7)<br/>
        📧 Email: {self.company_contact['email']}<br/>
        🌐 Website: {self.company_contact['website']}<br/>
        💬 WhatsApp: {self.company_contact['phone']}
        """
        elements.append(Paragraph(contact_info, styles['InvoiceInfo']))
        elements.append(Spacer(1, 30))

        # Footer with generation info
        generation_time = datetime.now().strftime('%B %d, %Y at %I:%M %p')
        footer_text = f"""
        This invoice was generated electronically by CVBioLabs on {generation_time}.<br/>
        For any queries regarding this invoice, please contact our support team.<br/>
        <b>CVBioLabs - For a Healthy Life</b>
        """
        elements.append(Paragraph(footer_text, styles['Footer']))

    def generate_invoice(self, invoice_data: Dict) -> io.BytesIO:
        """
        Generate a professional PDF invoice

        Args:
            invoice_data: Dictionary containing invoice information
                Required fields:
                - customer_name: str
                - customer_email: str
                - tests: List[Dict] with 'name', 'code', 'amount', 'quantity'
                - invoice_number: str
                - booking_id: str (optional)

                Optional fields:
                - customer_phone: str
                - address_line1, address_line2, city, state, postal_code, country: str
                - invoice_date: datetime
                - due_date: datetime
                - payment_status: str
                - payment_method: str
                - transaction_id: str
                - payment_date: datetime
                - tax_rate: float (percentage)
                - payment_terms: List[str]

        Returns:
            io.BytesIO: PDF file buffer
        """
        try:
            # Create PDF buffer
            buffer = io.BytesIO()

            # Create document
            doc = SimpleDocTemplate(
                buffer,
                pagesize=letter,
                rightMargin=0.75*inch,
                leftMargin=0.75*inch,
                topMargin=0.75*inch,
                bottomMargin=0.75*inch
            )

            # Create styles
            styles = self._create_styles()

            # Build document elements
            elements = []

            # Header
            self._create_header(elements, styles)

            # Invoice information
            self._create_invoice_info_section(elements, styles, invoice_data)

            # Test items table
            self._create_test_items_table(elements, styles, invoice_data)

            # Payment information
            self._create_payment_info_section(elements, styles, invoice_data)

            # Footer
            self._create_footer(elements, styles, invoice_data)

            # Build PDF
            doc.build(elements)
            buffer.seek(0)

            logger.info(f"Invoice generated successfully for customer: {invoice_data.get('customer_name')}")
            return buffer

        except Exception as e:
            logger.error(f"Error generating invoice: {str(e)}", exc_info=True)
            raise

    def generate_invoice_filename(self, invoice_data: Dict) -> str:
        """Generate a standardized filename for the invoice"""
        customer_name = invoice_data.get('customer_name', 'Customer')
        # Clean customer name for filename
        clean_name = ''.join(c for c in customer_name if c.isalnum() or c in (' ', '-', '_')).strip()
        clean_name = clean_name.replace(' ', '_')

        invoice_number = invoice_data.get('invoice_number', 'INV000')
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        return f"CVBioLabs_Invoice_{invoice_number}_{clean_name}_{timestamp}.pdf"


# Convenience function for easy import
def generate_invoice(invoice_data: Dict) -> io.BytesIO:
    """
    Convenience function to generate an invoice

    Args:
        invoice_data: Dictionary containing invoice information

    Returns:
        io.BytesIO: PDF file buffer
    """
    generator = CVBioLabsInvoiceGenerator()
    return generator.generate_invoice(invoice_data)
