"""
CVBioLabs Patient Bill Generator
Generates professional PDF bills for patients matching the sample format
Used by staff/receptionist in patient records section
"""

import io
import os
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import (
    SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, 
    Image, KeepTogether
)
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT, TA_JUSTIFY

# Set up logging
logger = logging.getLogger(__name__)

class CVBioLabsPatientBillGenerator:
    """Professional patient bill generator matching the sample format"""
    
    def __init__(self):
        self.company_name = "CV BIOLABS"
        self.company_tagline = "For a healthy life"
        self.company_address = [
            "H.No-1624, Old MIG, Phase-II, BHEL",
            "<PERSON><PERSON>, Hyderabad-500032",
            "Contact us at Mobile: +91 78936 20683"
        ]
        
        # CVBioLabs brand colors
        self.colors = {
            'primary_blue': colors.Color(0, 47/255, 108/255),      # #002f6c
            'primary_orange': colors.Color(244/255, 124/255, 32/255), # #f47c20
            'light_gray': colors.Color(248/255, 249/255, 250/255), # #f8f9fa
            'text_dark': colors.Color(51/255, 51/255, 51/255),     # #333333
            'white': colors.white,
            'black': colors.black
        }
        
    def _get_logo_path(self) -> Optional[str]:
        """Get the path to the company logo"""
        logo_path = "static/images/CV.png"
        if os.path.exists(logo_path):
            return logo_path
        return None
    
    def _create_styles(self):
        """Create custom paragraph styles for the bill"""
        styles = getSampleStyleSheet()
        
        # Company name style
        styles.add(ParagraphStyle(
            name='CompanyName',
            parent=styles['Heading1'],
            fontSize=18,
            textColor=self.colors['primary_orange'],
            alignment=TA_CENTER,
            spaceAfter=4,
            fontName='Helvetica-Bold'
        ))
        
        # Company tagline style
        styles.add(ParagraphStyle(
            name='CompanyTagline',
            parent=styles['Normal'],
            fontSize=10,
            textColor=self.colors['primary_blue'],
            alignment=TA_CENTER,
            spaceAfter=10,
            fontName='Helvetica-Oblique'
        ))
        
        # Address style
        styles.add(ParagraphStyle(
            name='Address',
            parent=styles['Normal'],
            fontSize=9,
            textColor=self.colors['text_dark'],
            alignment=TA_CENTER,
            spaceAfter=3,
            fontName='Helvetica'
        ))
        
        # Patient info style
        styles.add(ParagraphStyle(
            name='PatientInfo',
            parent=styles['Normal'],
            fontSize=10,
            textColor=self.colors['text_dark'],
            alignment=TA_LEFT,
            spaceAfter=3,
            fontName='Helvetica'
        ))
        
        # Bill info style
        styles.add(ParagraphStyle(
            name='BillInfo',
            parent=styles['Normal'],
            fontSize=10,
            textColor=self.colors['text_dark'],
            alignment=TA_RIGHT,
            spaceAfter=3,
            fontName='Helvetica'
        ))
        
        return styles
    
    def _create_header(self, elements: List, styles: Dict) -> None:
        """Create the bill header with logo and company information"""
        # Add logo if available
        logo_path = self._get_logo_path()
        if logo_path:
            try:
                logo = Image(logo_path, width=1.5*inch, height=0.6*inch)
                logo.hAlign = 'CENTER'
                elements.append(logo)
                elements.append(Spacer(1, 5))
            except Exception as e:
                logger.warning(f"Could not load logo: {e}")
                # Fallback to text logo
                elements.append(Paragraph(self.company_name, styles['CompanyName']))
        else:
            # Text-based logo
            elements.append(Paragraph(self.company_name, styles['CompanyName']))
        
        # Company tagline
        elements.append(Paragraph(self.company_tagline, styles['CompanyTagline']))
        
        # Company address
        for line in self.company_address:
            elements.append(Paragraph(line, styles['Address']))
        
        elements.append(Spacer(1, 15))
    
    def _create_patient_and_bill_info(self, elements: List, styles: Dict, bill_data: Dict) -> None:
        """Create patient information and bill details section"""
        # Create two-column layout for patient info and bill info
        patient_info = []
        patient_info.append(f"<b>Patient Name:</b> {bill_data.get('patient_name', 'N/A')}")
        patient_info.append(f"<b>Age:</b> {bill_data.get('patient_age', 'N/A')}")
        patient_info.append(f"<b>Gender:</b> {bill_data.get('patient_gender', 'N/A')}")
        patient_info.append(f"<b>Phone:</b> {bill_data.get('patient_phone', 'N/A')}")
        patient_info.append(f"<b>Referred by:</b> {bill_data.get('referred_by', 'Self')}")
        
        bill_info = []
        bill_info.append(f"<b>Patient ID:</b> {bill_data.get('patient_id', 'N/A')}")
        bill_info.append(f"<b>Bill No:</b> {bill_data.get('bill_number', 'N/A')}")
        bill_info.append(f"<b>Bill Date:</b> {bill_data.get('bill_date', datetime.now()).strftime('%d/%m/%Y')}")
        bill_info.append(f"<b>Collection Date:</b> {bill_data.get('collection_date', 'N/A')}")
        bill_info.append(f"<b>Reporting Date:</b> {bill_data.get('reporting_date', 'N/A')}")
        
        # Create table for layout
        info_data = [
            [
                Paragraph('<br/>'.join(patient_info), styles['PatientInfo']),
                Paragraph('<br/>'.join(bill_info), styles['BillInfo'])
            ]
        ]
        
        info_table = Table(info_data, colWidths=[3.5*inch, 3.5*inch])
        info_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 0),
        ]))
        
        elements.append(info_table)
        elements.append(Spacer(1, 20))
    
    def _create_test_details_table(self, elements: List, styles: Dict, bill_data: Dict) -> None:
        """Create the test details table matching the sample format"""
        elements.append(Paragraph("<b>Test Description</b>", styles['PatientInfo']))
        elements.append(Spacer(1, 10))
        
        # Table headers
        headers = ['S.No', 'Test Description', 'Method', 'Reference Range', 'Units', 'Result']
        table_data = [headers]
        
        # Add test items
        tests = bill_data.get('tests', [])
        
        for i, test in enumerate(tests, 1):
            test_name = test.get('name', 'N/A')
            method = test.get('method', 'N/A')
            reference_range = test.get('reference_range', 'N/A')
            units = test.get('units', 'N/A')
            result = test.get('result', 'N/A')
            
            table_data.append([
                str(i),
                test_name,
                method,
                reference_range,
                units,
                result
            ])
        
        # Create table
        test_table = Table(table_data, colWidths=[0.5*inch, 2.5*inch, 1.2*inch, 1.2*inch, 0.8*inch, 1*inch])
        
        # Style the table
        test_table.setStyle(TableStyle([
            # Header styling
            ('BACKGROUND', (0, 0), (-1, 0), self.colors['primary_blue']),
            ('TEXTCOLOR', (0, 0), (-1, 0), self.colors['white']),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 9),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('VALIGN', (0, 0), (-1, 0), 'MIDDLE'),
            
            # Data rows styling
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('ALIGN', (0, 1), (0, -1), 'CENTER'),  # S.No column
            ('ALIGN', (1, 1), (-1, -1), 'LEFT'),   # Other columns
            ('VALIGN', (0, 1), (-1, -1), 'MIDDLE'),
            
            # Alternating row colors
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [self.colors['white'], self.colors['light_gray']]),
            
            # Grid lines
            ('GRID', (0, 0), (-1, -1), 1, self.colors['text_dark']),
            ('LINEBELOW', (0, 0), (-1, 0), 2, self.colors['primary_blue']),
            
            # Padding
            ('LEFTPADDING', (0, 0), (-1, -1), 4),
            ('RIGHTPADDING', (0, 0), (-1, -1), 4),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        
        elements.append(test_table)
        elements.append(Spacer(1, 20))
    
    def _create_footer_notes(self, elements: List, styles: Dict, bill_data: Dict) -> None:
        """Create footer notes section"""
        notes = [
            "1. Values outside the normal range are marked with an asterisk (*) and are printed in bold.",
            "2. A report of the laboratory examination is not a medical diagnosis, as well as it is not a substitute for a medical consultation.",
            "3. This report should be correlated with clinical findings.",
            "4. In case of any discrepancy, please contact the laboratory within 7 days.",
            "5. A duplicate of this report will be issued only with the consent of the patient."
        ]
        
        elements.append(Paragraph("<b>Note:</b>", styles['PatientInfo']))
        elements.append(Spacer(1, 5))
        
        for note in notes:
            elements.append(Paragraph(note, styles['PatientInfo']))
            elements.append(Spacer(1, 3))
        
        elements.append(Spacer(1, 20))
        
        # Signature section
        signature_data = [
            ['', '', 'Authorized Signatory'],
            ['', '', 'CV BIOLABS']
        ]
        
        signature_table = Table(signature_data, colWidths=[2*inch, 2*inch, 3*inch])
        signature_table.setStyle(TableStyle([
            ('FONTNAME', (2, 0), (2, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (2, 0), (2, -1), 10),
            ('ALIGN', (2, 0), (2, -1), 'CENTER'),
            ('VALIGN', (2, 0), (2, -1), 'BOTTOM'),
            ('LINEABOVE', (2, 0), (2, 0), 1, self.colors['text_dark']),
        ]))
        
        elements.append(signature_table)
    
    def generate_patient_bill(self, bill_data: Dict) -> io.BytesIO:
        """
        Generate a professional PDF patient bill matching the sample format
        
        Args:
            bill_data: Dictionary containing bill information
                Required fields:
                - patient_name: str
                - patient_id: str
                - bill_number: str
                - tests: List[Dict] with test details
                
                Optional fields:
                - patient_age, patient_gender, patient_phone: str
                - referred_by: str
                - bill_date, collection_date, reporting_date: datetime or str
        
        Returns:
            io.BytesIO: PDF file buffer
        """
        try:
            # Create PDF buffer
            buffer = io.BytesIO()
            
            # Create document
            doc = SimpleDocTemplate(
                buffer,
                pagesize=letter,
                rightMargin=0.75*inch,
                leftMargin=0.75*inch,
                topMargin=0.5*inch,
                bottomMargin=0.5*inch
            )
            
            # Create styles
            styles = self._create_styles()
            
            # Build document elements
            elements = []
            
            # Header
            self._create_header(elements, styles)
            
            # Patient and bill information
            self._create_patient_and_bill_info(elements, styles, bill_data)
            
            # Test details table
            self._create_test_details_table(elements, styles, bill_data)
            
            # Footer notes
            self._create_footer_notes(elements, styles, bill_data)
            
            # Build PDF
            doc.build(elements)
            buffer.seek(0)
            
            logger.info(f"Patient bill generated successfully for: {bill_data.get('patient_name')}")
            return buffer
            
        except Exception as e:
            logger.error(f"Error generating patient bill: {str(e)}", exc_info=True)
            raise
    
    def generate_bill_filename(self, bill_data: Dict) -> str:
        """Generate a standardized filename for the patient bill"""
        patient_name = bill_data.get('patient_name', 'Patient')
        # Clean patient name for filename
        clean_name = ''.join(c for c in patient_name if c.isalnum() or c in (' ', '-', '_')).strip()
        clean_name = clean_name.replace(' ', '_')
        
        bill_number = bill_data.get('bill_number', 'BILL000')
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        return f"CVBioLabs_Bill_{bill_number}_{clean_name}_{timestamp}.pdf"


# Convenience function for easy import
def generate_patient_bill(bill_data: Dict) -> io.BytesIO:
    """
    Convenience function to generate a patient bill
    
    Args:
        bill_data: Dictionary containing bill information
    
    Returns:
        io.BytesIO: PDF file buffer
    """
    generator = CVBioLabsPatientBillGenerator()
    return generator.generate_patient_bill(bill_data)
