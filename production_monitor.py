#!/usr/bin/env python3
"""
Production monitoring script for CVBioLabs
Monitors application health, performance, and security
"""

import os
import sys
import time
import json
import requests
import subprocess
import psutil
from datetime import datetime, timedelta
from pathlib import Path

class CVBioLabsMonitor:
    def __init__(self):
        self.base_url = os.getenv('DOMAIN_NAME', 'localhost')
        self.log_file = 'logs/monitor.log'
        self.alert_threshold = {
            'cpu_percent': 80,
            'memory_percent': 85,
            'disk_percent': 90,
            'response_time': 5.0  # seconds
        }
        
    def log_message(self, message, level='INFO'):
        """Log message with timestamp"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {level}: {message}"
        print(log_entry)
        
        # Ensure logs directory exists
        os.makedirs('logs', exist_ok=True)
        
        with open(self.log_file, 'a') as f:
            f.write(log_entry + '\n')
    
    def check_docker_services(self):
        """Check if Docker services are running"""
        services = ['web', 'db', 'redis', 'nginx']
        results = {}
        
        try:
            result = subprocess.run(['docker-compose', 'ps', '--format', 'json'], 
                                  capture_output=True, text=True, check=True)
            
            if result.stdout.strip():
                containers = [json.loads(line) for line in result.stdout.strip().split('\n')]
                
                for service in services:
                    service_running = any(
                        container['Service'] == service and container['State'] == 'running'
                        for container in containers
                    )
                    results[service] = service_running
                    
                    if service_running:
                        self.log_message(f"✅ Service {service} is running")
                    else:
                        self.log_message(f"❌ Service {service} is not running", 'ERROR')
            else:
                self.log_message("❌ No Docker containers found", 'ERROR')
                
        except subprocess.CalledProcessError as e:
            self.log_message(f"❌ Error checking Docker services: {e}", 'ERROR')
            
        return results
    
    def check_website_health(self):
        """Check website health and response time"""
        health_url = f"http://{self.base_url}/health"
        
        try:
            start_time = time.time()
            response = requests.get(health_url, timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                self.log_message(f"✅ Website health check passed (Response time: {response_time:.2f}s)")
                
                if response_time > self.alert_threshold['response_time']:
                    self.log_message(f"⚠️  Slow response time: {response_time:.2f}s", 'WARNING')
                
                return True, response_time
            else:
                self.log_message(f"❌ Website health check failed: HTTP {response.status_code}", 'ERROR')
                return False, response_time
                
        except requests.RequestException as e:
            self.log_message(f"❌ Website health check failed: {e}", 'ERROR')
            return False, None
    
    def check_system_resources(self):
        """Check system resource usage"""
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        if cpu_percent > self.alert_threshold['cpu_percent']:
            self.log_message(f"⚠️  High CPU usage: {cpu_percent}%", 'WARNING')
        else:
            self.log_message(f"✅ CPU usage: {cpu_percent}%")
        
        # Memory usage
        memory = psutil.virtual_memory()
        if memory.percent > self.alert_threshold['memory_percent']:
            self.log_message(f"⚠️  High memory usage: {memory.percent}%", 'WARNING')
        else:
            self.log_message(f"✅ Memory usage: {memory.percent}%")
        
        # Disk usage
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        if disk_percent > self.alert_threshold['disk_percent']:
            self.log_message(f"⚠️  High disk usage: {disk_percent:.1f}%", 'WARNING')
        else:
            self.log_message(f"✅ Disk usage: {disk_percent:.1f}%")
        
        return {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'disk_percent': disk_percent
        }
    
    def check_database_connection(self):
        """Check database connectivity"""
        try:
            result = subprocess.run([
                'docker-compose', 'exec', '-T', 'db', 
                'mysql', '-u', 'root', f'-p{os.getenv("DB_ROOT_PASSWORD", "")}',
                '-e', 'SELECT 1'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                self.log_message("✅ Database connection successful")
                return True
            else:
                self.log_message(f"❌ Database connection failed: {result.stderr}", 'ERROR')
                return False
                
        except subprocess.TimeoutExpired:
            self.log_message("❌ Database connection timeout", 'ERROR')
            return False
        except Exception as e:
            self.log_message(f"❌ Database check error: {e}", 'ERROR')
            return False
    
    def check_ssl_certificate(self):
        """Check SSL certificate expiration"""
        domain = self.base_url.replace('http://', '').replace('https://', '')
        
        try:
            result = subprocess.run([
                'openssl', 's_client', '-connect', f'{domain}:443', '-servername', domain
            ], input='', capture_output=True, text=True, timeout=10)
            
            if 'CONNECTED' in result.stdout:
                # Extract certificate expiration date
                cert_result = subprocess.run([
                    'openssl', 'x509', '-noout', '-dates'
                ], input=result.stdout, capture_output=True, text=True)
                
                if cert_result.returncode == 0:
                    self.log_message("✅ SSL certificate is valid")
                    return True
                    
        except Exception as e:
            self.log_message(f"⚠️  Could not check SSL certificate: {e}", 'WARNING')
            
        return False
    
    def check_log_files(self):
        """Check for errors in log files"""
        log_files = [
            'logs/app.log',
            'logs/gunicorn_error.log',
            'logs/security.log'
        ]
        
        error_count = 0
        
        for log_file in log_files:
            if os.path.exists(log_file):
                try:
                    # Check last 100 lines for errors
                    result = subprocess.run(['tail', '-100', log_file], 
                                          capture_output=True, text=True)
                    
                    error_lines = [line for line in result.stdout.split('\n') 
                                 if 'ERROR' in line.upper() or 'CRITICAL' in line.upper()]
                    
                    if error_lines:
                        recent_errors = [line for line in error_lines 
                                       if self._is_recent_log_entry(line)]
                        
                        if recent_errors:
                            error_count += len(recent_errors)
                            self.log_message(f"⚠️  Found {len(recent_errors)} recent errors in {log_file}", 'WARNING')
                            
                except Exception as e:
                    self.log_message(f"⚠️  Could not check {log_file}: {e}", 'WARNING')
        
        if error_count == 0:
            self.log_message("✅ No recent errors found in log files")
        
        return error_count
    
    def _is_recent_log_entry(self, log_line, hours=1):
        """Check if log entry is from the last N hours"""
        try:
            # Simple check - this would need to be more sophisticated for production
            now = datetime.now()
            cutoff = now - timedelta(hours=hours)
            return True  # Simplified for this example
        except:
            return False
    
    def restart_failed_services(self):
        """Restart any failed services"""
        services = self.check_docker_services()
        
        for service, is_running in services.items():
            if not is_running:
                self.log_message(f"🔄 Attempting to restart {service}", 'WARNING')
                try:
                    subprocess.run(['docker-compose', 'restart', service], 
                                 check=True, timeout=60)
                    self.log_message(f"✅ Successfully restarted {service}")
                except subprocess.CalledProcessError as e:
                    self.log_message(f"❌ Failed to restart {service}: {e}", 'ERROR')
    
    def generate_report(self):
        """Generate monitoring report"""
        self.log_message("🏥 CVBioLabs Health Check Report")
        self.log_message("=" * 40)
        
        # Check all systems
        docker_services = self.check_docker_services()
        website_health, response_time = self.check_website_health()
        system_resources = self.check_system_resources()
        database_ok = self.check_database_connection()
        ssl_ok = self.check_ssl_certificate()
        error_count = self.check_log_files()
        
        # Summary
        all_services_ok = all(docker_services.values())
        
        if all_services_ok and website_health and database_ok:
            self.log_message("🎉 All systems operational!")
        else:
            self.log_message("⚠️  Some issues detected - check logs above", 'WARNING')
            self.restart_failed_services()
        
        return {
            'docker_services': docker_services,
            'website_health': website_health,
            'response_time': response_time,
            'system_resources': system_resources,
            'database_ok': database_ok,
            'ssl_ok': ssl_ok,
            'error_count': error_count
        }

def main():
    """Main monitoring function"""
    monitor = CVBioLabsMonitor()
    
    if len(sys.argv) > 1 and sys.argv[1] == '--continuous':
        # Continuous monitoring mode
        while True:
            monitor.generate_report()
            time.sleep(300)  # Check every 5 minutes
    else:
        # Single check
        monitor.generate_report()

if __name__ == '__main__':
    main()
