#!/bin/bash

# CVBioLabs Quick Start Script for DigitalOcean
# This script provides an interactive setup for first-time deployment

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${PURPLE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    CVBioLabs Quick Start                     ║"
echo "║              Production Deployment Assistant                 ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Function to prompt for input with default
prompt_with_default() {
    local prompt="$1"
    local default="$2"
    local result
    
    if [ -n "$default" ]; then
        read -p "$prompt [$default]: " result
        echo "${result:-$default}"
    else
        read -p "$prompt: " result
        echo "$result"
    fi
}

# Function to generate secure password
generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
}

echo -e "${BLUE}🚀 Welcome to CVBioLabs Production Deployment!${NC}"
echo ""
echo "This script will help you set up CVBioLabs on your DigitalOcean server."
echo "Please have the following information ready:"
echo "• Your domain name"
echo "• Admin email address"
echo "• Email server credentials"
echo "• Razorpay credentials (if using payments)"
echo ""

read -p "Press Enter to continue..."

# Collect configuration information
echo -e "\n${YELLOW}📋 Configuration Setup${NC}"
echo "================================"

DOMAIN_NAME=$(prompt_with_default "Enter your domain name (e.g., yourdomain.com)" "")
ADMIN_EMAIL=$(prompt_with_default "Enter admin email address" "admin@$DOMAIN_NAME")
ADMIN_NAME=$(prompt_with_default "Enter admin name" "Administrator")

echo ""
echo -e "${YELLOW}🔐 Security Configuration${NC}"
echo "================================"

SECRET_KEY=$(generate_password)
DB_PASSWORD=$(generate_password)
DB_ROOT_PASSWORD=$(generate_password)
REDIS_PASSWORD=$(generate_password)

echo "Generated secure passwords for:"
echo "• Flask Secret Key"
echo "• Database Password"
echo "• Database Root Password"
echo "• Redis Password"

echo ""
echo -e "${YELLOW}📧 Email Configuration${NC}"
echo "================================"

MAIL_SERVER=$(prompt_with_default "SMTP server" "smtpout.secureserver.net")
MAIL_PORT=$(prompt_with_default "SMTP port" "465")
MAIL_USE_SSL=$(prompt_with_default "Use SSL (true/false)" "true")
MAIL_USERNAME=$(prompt_with_default "Email username" "$ADMIN_EMAIL")
MAIL_PASSWORD=$(prompt_with_default "Email password" "")

echo ""
echo -e "${YELLOW}💳 Payment Gateway (Optional)${NC}"
echo "================================"

read -p "Do you want to configure Razorpay payment gateway? (y/n): " setup_razorpay

if [[ $setup_razorpay =~ ^[Yy]$ ]]; then
    RAZORPAY_KEY_ID=$(prompt_with_default "Razorpay Key ID" "")
    RAZORPAY_KEY_SECRET=$(prompt_with_default "Razorpay Key Secret" "")
else
    RAZORPAY_KEY_ID="your-razorpay-key-id"
    RAZORPAY_KEY_SECRET="your-razorpay-key-secret"
fi

# Create .env.production file
echo ""
echo -e "${BLUE}📝 Creating production configuration...${NC}"

cat > .env.production << EOF
# CVBioLabs Production Environment Configuration
# Generated by quick-start.sh on $(date)

# Flask Configuration
FLASK_ENV=production
SECRET_KEY=$SECRET_KEY
FLASK_APP=app.py

# Database Configuration
DB_HOST=db
DB_USER=cvbiolabs_user
DB_PASSWORD=$DB_PASSWORD
DB_NAME=cvbiolabs
DB_CHARSET=utf8mb4
DB_ROOT_PASSWORD=$DB_ROOT_PASSWORD

# Redis Configuration
REDIS_URL=redis://:$REDIS_PASSWORD@redis:6379/0
REDIS_PASSWORD=$REDIS_PASSWORD

# Email Configuration
MAIL_SERVER=$MAIL_SERVER
MAIL_PORT=$MAIL_PORT
MAIL_USE_TLS=false
MAIL_USE_SSL=$MAIL_USE_SSL
MAIL_USERNAME=$MAIL_USERNAME
MAIL_PASSWORD=$MAIL_PASSWORD
MAIL_DEFAULT_SENDER=$MAIL_USERNAME
MAIL_SUPPRESS_SEND=false

# Payment Gateway
RAZORPAY_KEY_ID=$RAZORPAY_KEY_ID
RAZORPAY_KEY_SECRET=$RAZORPAY_KEY_SECRET

# Domain Configuration
DOMAIN_NAME=$DOMAIN_NAME
ALLOWED_HOSTS=$DOMAIN_NAME,www.$DOMAIN_NAME

# Security Settings
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Lax

# File Upload Settings
MAX_CONTENT_LENGTH=52428800
UPLOAD_FOLDER=/app/uploads

# Logging
LOG_LEVEL=INFO
LOG_FILE=/app/logs/app.log

# Backup Settings
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE=0 2 * * *

# Monitoring
HEALTH_CHECK_INTERVAL=30
METRICS_ENABLED=true

# Rate Limiting
RATE_LIMIT_STORAGE_URL=redis://:$REDIS_PASSWORD@redis:6379/1
EOF

echo -e "${GREEN}✅ Configuration file created: .env.production${NC}"

# Update nginx configuration
echo -e "${BLUE}🌐 Updating nginx configuration...${NC}"
sed -i "s/YOUR_DOMAIN.com/$DOMAIN_NAME/g" nginx/sites-available/cvbiolabs.conf
echo -e "${GREEN}✅ Nginx configuration updated${NC}"

# Make scripts executable
echo -e "${BLUE}🔧 Setting up scripts...${NC}"
chmod +x deploy.sh
chmod +x backup.sh
chmod +x production_monitor.py
chmod +x init_production_db.py
echo -e "${GREEN}✅ Scripts are now executable${NC}"

# Show next steps
echo ""
echo -e "${GREEN}🎉 Configuration Complete!${NC}"
echo ""
echo -e "${YELLOW}📋 Next Steps:${NC}"
echo "1. Ensure your domain $DOMAIN_NAME points to this server's IP"
echo "2. Run the deployment script:"
echo -e "   ${BLUE}./deploy.sh $DOMAIN_NAME $ADMIN_EMAIL${NC}"
echo "3. Follow the deployment checklist in DEPLOYMENT_CHECKLIST.md"
echo ""
echo -e "${YELLOW}🔐 Important Security Information:${NC}"
echo "Your generated passwords have been saved to .env.production"
echo "Please keep this file secure and never commit it to version control!"
echo ""
echo -e "${YELLOW}📚 Documentation:${NC}"
echo "• Full deployment guide: PRODUCTION_DEPLOYMENT.md"
echo "• Deployment checklist: DEPLOYMENT_CHECKLIST.md"
echo "• Monitor your application: ./production_monitor.py"
echo "• Create backups: ./backup.sh"
echo ""

# Ask if user wants to start deployment now
echo -e "${BLUE}🚀 Ready to Deploy?${NC}"
read -p "Do you want to start the deployment now? (y/n): " start_deployment

if [[ $start_deployment =~ ^[Yy]$ ]]; then
    echo ""
    echo -e "${GREEN}🚀 Starting deployment...${NC}"
    ./deploy.sh "$DOMAIN_NAME" "$ADMIN_EMAIL"
else
    echo ""
    echo -e "${YELLOW}⏸️  Deployment paused.${NC}"
    echo "When you're ready, run:"
    echo -e "${BLUE}./deploy.sh $DOMAIN_NAME $ADMIN_EMAIL${NC}"
fi

echo ""
echo -e "${PURPLE}Thank you for using CVBioLabs! 🏥${NC}"
