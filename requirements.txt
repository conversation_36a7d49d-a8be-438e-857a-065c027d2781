# CVBioLabs - Python Dependencies
# Generated for Python 3.8+ compatibility

# Core Flask Framework
Flask>=2.2.0,<3.0.0
Werkzeug>=2.2.0,<3.0.0

# Flask Extensions
Flask-Session>=0.4.0
Flask-Mail>=0.9.1
Flask-WTF>=1.0.0
Flask-Login>=0.6.0
Flask-CORS>=3.0.0
Flask-Limiter>=2.0.0

# Security & Authentication
bcrypt>=4.0.0
pyotp>=2.8.0
cryptography>=40.0.0

# Database Connectors
mysql-connector-python>=8.0.0
PyMySQL>=1.0.0
redis>=4.0.0

# Payment Gateway
razorpay>=1.3.0

# Environment & Configuration
python-dotenv>=0.19.0

# HTTP Requests
requests>=2.28.0

# Document Generation
openpyxl>=3.0.0
reportlab>=3.6.0

# Image Processing
Pillow>=9.0.0

# Development Dependencies (uncomment for development)
# pytest>=7.0.0
# pytest-flask>=1.2.0
# black>=22.0.0
# flake8>=5.0.0

# Production Server
gunicorn>=20.0.0

# Additional utilities that might be needed
WTForms>=3.0.0
MarkupSafe>=2.1.0
Jinja2>=3.1.0
itsdangerous>=2.1.0
click>=8.0.0
