"""
Security utilities for CVBioLabs application
Provides secure authentication, validation, and cryptographic functions
"""

import bcrypt
import secrets
import hashlib
import time
import re
import pyotp
import base64
from email.utils import parseaddr

from cryptography.fernet import Fe<PERSON>t
from datetime import datetime, timedelta
import os
import logging

logger = logging.getLogger(__name__)

class SecureAuth:
    """Secure authentication utilities"""
    
    @staticmethod
    def hash_password(password: str) -> str:
        """
        Securely hash a password using bcrypt with random salt
        """
        if not password:
            raise ValueError("Password cannot be empty")
        
        # Generate a random salt and hash the password
        salt = bcrypt.gensalt(rounds=12)  # Higher rounds for better security
        password_hash = bcrypt.hashpw(password.encode('utf-8'), salt)
        return password_hash.decode('utf-8')
    
    @staticmethod
    def verify_password(password: str, password_hash: str) -> bool:
        """
        Verify a password against its hash
        """
        try:
            if not password or not password_hash:
                return False
            return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
        except Exception as e:
            logger.warning(f"Password verification failed: {str(e)}")
            return False
    
    @staticmethod
    def generate_secure_token(length: int = 32) -> str:
        """
        Generate a cryptographically secure random token
        """
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def generate_otp() -> str:
        """
        Generate a secure 6-digit OTP
        """
        return ''.join([str(secrets.randbelow(10)) for _ in range(6)])

class SecureOTP:
    """Secure OTP management with proper key handling"""
    
    def __init__(self):
        # Use environment variable for OTP secret, generate if not exists
        self.secret = os.getenv('OTP_SECRET_KEY')
        if not self.secret:
            # Generate a new secret and warn about it
            self.secret = pyotp.random_base32()
            logger.warning("OTP_SECRET_KEY not found in environment. Generated temporary secret.")
            logger.warning("Please set OTP_SECRET_KEY in your environment variables for production!")
    
    def generate_totp(self) -> str:
        """Generate time-based OTP"""
        totp = pyotp.TOTP(self.secret)
        return totp.now()
    
    def verify_totp(self, token: str, valid_window: int = 1) -> bool:
        """Verify time-based OTP with configurable window"""
        try:
            totp = pyotp.TOTP(self.secret)
            return totp.verify(token, valid_window=valid_window)
        except Exception as e:
            logger.warning(f"TOTP verification failed: {str(e)}")
            return False

class InputValidator:
    """Input validation utilities"""
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """
        Validate email format using RFC-compliant parsing
        """
        if not email:
            return False
        
        try:
            # Parse email address
            parsed = parseaddr(email)
            if not parsed[1]:  # No email part
                return False
            
            # Basic regex validation
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            return bool(re.match(email_pattern, parsed[1]))
        except Exception:
            return False
    
    @staticmethod
    def validate_phone(phone: str) -> bool:
        """
        Validate phone number format (Indian format)
        """
        if not phone:
            return False
        
        # Remove all non-digit characters
        digits_only = re.sub(r'\D', '', phone)
        
        # Check for valid Indian phone number patterns
        # 10 digits starting with 6-9, or 11 digits starting with 0, or 12 digits starting with 91
        patterns = [
            r'^[6-9]\d{9}$',      # 10 digits starting with 6-9
            r'^0[6-9]\d{9}$',     # 11 digits starting with 0
            r'^91[6-9]\d{9}$'     # 12 digits starting with 91
        ]
        
        return any(re.match(pattern, digits_only) for pattern in patterns)
    
    @staticmethod
    def validate_password_strength(password: str) -> tuple[bool, str]:
        """
        Validate password strength
        Returns (is_valid, error_message)
        """
        if not password:
            return False, "Password cannot be empty"
        
        if len(password) < 8:
            return False, "Password must be at least 8 characters long"
        
        if len(password) > 128:
            return False, "Password must be less than 128 characters"
        
        # Check for at least one uppercase letter
        if not re.search(r'[A-Z]', password):
            return False, "Password must contain at least one uppercase letter"
        
        # Check for at least one lowercase letter
        if not re.search(r'[a-z]', password):
            return False, "Password must contain at least one lowercase letter"
        
        # Check for at least one digit
        if not re.search(r'\d', password):
            return False, "Password must contain at least one digit"
        
        # Check for at least one special character
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            return False, "Password must contain at least one special character"
        
        return True, "Password is strong"
    
    @staticmethod
    def sanitize_input(input_str: str) -> str:
        """
        Sanitize user input to prevent XSS
        """
        if not input_str:
            return ""
        
        # Remove potentially dangerous characters
        sanitized = re.sub(r'[<>"\']', '', str(input_str))
        return sanitized.strip()

class SecureSignature:
    """Secure digital signature implementation"""
    
    def __init__(self):
        # Use environment variable for signature key
        key_b64 = os.getenv('SIGNATURE_KEY')
        if not key_b64:
            # Generate a new key and warn about it
            self.key = Fernet.generate_key()
            logger.warning("SIGNATURE_KEY not found in environment. Generated temporary key.")
            logger.warning("Please set SIGNATURE_KEY in your environment variables for production!")
            logger.warning(f"Generated key (base64): {base64.b64encode(self.key).decode()}")
        else:
            try:
                # The key should already be a valid Fernet key (base64 encoded)
                self.key = key_b64.encode()
            except Exception as e:
                logger.error(f"Invalid SIGNATURE_KEY format: {e}")
                raise ValueError("Invalid SIGNATURE_KEY format in environment")
        
        self.fernet = Fernet(self.key)
    
    def generate_signature(self, data: str) -> str:
        """Generate a digital signature for the given data"""
        try:
            # Add timestamp to prevent replay attacks
            timestamped_data = f"{data}:{int(time.time())}"
            signature = self.fernet.encrypt(timestamped_data.encode())
            return base64.b64encode(signature).decode()
        except Exception as e:
            logger.error(f"Signature generation failed: {e}")
            raise
    
    def verify_signature(self, data: str, signature: str, max_age: int = 3600) -> bool:
        """
        Verify the digital signature
        max_age: maximum age of signature in seconds (default 1 hour)
        """
        try:
            decoded_signature = base64.b64decode(signature.encode())
            decrypted_data = self.fernet.decrypt(decoded_signature).decode()
            
            # Split data and timestamp
            parts = decrypted_data.rsplit(':', 1)
            if len(parts) != 2:
                return False
            
            original_data, timestamp_str = parts
            
            # Verify data matches
            if original_data != data:
                return False
            
            # Verify timestamp is not too old
            timestamp = int(timestamp_str)
            current_time = int(time.time())
            
            if current_time - timestamp > max_age:
                logger.warning("Signature expired")
                return False
            
            return True
        except Exception as e:
            logger.warning(f"Signature verification failed: {e}")
            return False

# Initialize global instances
secure_auth = SecureAuth()
secure_otp = SecureOTP()
input_validator = InputValidator()
secure_signature = SecureSignature()
