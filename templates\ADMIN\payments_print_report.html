<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payments Report - CVBioLabs</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        /* Print-specific styles */
        @media print {
            @page {
                margin: 0.5in;
                size: A4;
            }

            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            body {
                margin: 0;
                padding: 0;
                font-family: 'Inter', Arial, sans-serif;
                font-size: 10px;
                line-height: 1.3;
                color: #333;
                background: white !important;
            }

            .no-print {
                display: none !important;
            }

            .print-container {
                width: 100%;
                max-width: none;
                margin: 0;
                padding: 12px;
                box-shadow: none;
            }

            .page-break {
                page-break-before: always;
            }

            .keep-together {
                page-break-inside: avoid;
            }

            table {
                page-break-inside: auto;
            }

            tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }

            thead {
                display: table-header-group;
            }

            tfoot {
                display: table-footer-group;
            }
        }
        
        /* Screen styles */
        @media screen {
            body {
                background-color: #f8f9fa;
                font-family: 'Inter', Arial, sans-serif;
                padding: 16px;
                font-size: 11px;
                line-height: 1.35;
            }

            .print-container {
                max-width: 210mm;
                margin: 0 auto;
                background: white;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
                padding: 24px;
            }

            .print-controls {
                text-align: center;
                margin-bottom: 16px;
            }

            .btn-print {
                background: #002f6c;
                color: white;
                border: none;
                padding: 10px 24px;
                border-radius: 4px;
                font-weight: 500;
                font-size: 11px;
                cursor: pointer;
                margin-right: 8px;
            }

            .btn-print:hover {
                background: #001a3d;
            }

            .btn-back {
                background: #6c757d;
                color: white;
                border: none;
                padding: 10px 24px;
                border-radius: 4px;
                font-weight: 500;
                font-size: 11px;
                cursor: pointer;
                text-decoration: none;
                display: inline-block;
            }

            .btn-back:hover {
                background: #545b62;
                color: white;
                text-decoration: none;
            }
        }
        
        /* Common styles */
        .report-header {
            text-align: center;
            margin-bottom: 24px;
            border-bottom: 2px solid #002f6c;
            padding-bottom: 16px;
        }

        .company-logo {
            max-height: 65px;
            width: auto;
            margin-bottom: 12px;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }

        .company-name {
            font-family: 'Poppins', sans-serif;
            font-size: 24px;
            font-weight: 600;
            color: #002f6c;
            margin: 8px 0 4px 0;
            letter-spacing: -0.3px;
            text-align: center;
        }

        .company-tagline {
            font-size: 12px;
            color: #f47c20;
            font-weight: 500;
            margin-bottom: 12px;
            text-align: center;
        }

        .report-title {
            font-family: 'Poppins', sans-serif;
            font-size: 18px;
            font-weight: 600;
            color: #002f6c;
            margin: 16px 0 8px 0;
            text-align: center;
        }
        
        .report-meta {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 24px;
            font-size: 9px;
        }

        .meta-section h6 {
            font-weight: 600;
            color: #002f6c;
            margin-bottom: 8px;
            font-size: 10px;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .meta-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            padding: 1px 0;
            align-items: center;
        }

        .meta-label {
            font-weight: 500;
            color: #666;
            text-align: left;
        }

        .meta-value {
            font-weight: 600;
            color: #333;
            text-align: right;
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }

        .summary-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
            text-align: center;
        }

        .summary-card h6 {
            font-size: 9px;
            font-weight: 600;
            color: #666;
            margin-bottom: 6px;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .summary-value {
            font-size: 14px;
            font-weight: 600;
            color: #002f6c;
            font-family: 'Courier New', monospace;
        }
        
        .payments-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 8px;
        }

        .payments-table th {
            background: #002f6c;
            color: white;
            padding: 8px 6px;
            text-align: left;
            font-weight: 600;
            font-size: 8px;
            text-transform: uppercase;
            letter-spacing: 0.2px;
            border: 1px solid #001a3d;
            vertical-align: middle;
        }

        .payments-table th.text-right {
            text-align: right;
        }

        .payments-table td {
            padding: 6px;
            border: 1px solid #dee2e6;
            vertical-align: middle;
            line-height: 1.2;
        }

        .payments-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }

        .payments-table tbody tr:hover {
            background: #e6f7ff;
        }
        
        .status-badge {
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 7px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.2px;
            display: inline-block;
        }

        .status-paid {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .status-refunded {
            background: #d1ecf1;
            color: #0c5460;
        }

        .amount-cell {
            text-align: right !important;
            font-weight: 600;
            font-family: 'Courier New', monospace;
            padding-right: 8px !important;
        }

        .text-right {
            text-align: right !important;
        }

        .text-left {
            text-align: left !important;
        }

        .text-center {
            text-align: center !important;
        }
        
        .report-footer {
            margin-top: 24px;
            padding-top: 12px;
            border-top: 1px solid #e9ecef;
            text-align: center;
            font-size: 8px;
            color: #666;
            line-height: 1.3;
        }

        .footer-note {
            margin-bottom: 8px;
            font-style: italic;
        }

        .footer-contact {
            font-weight: 500;
            margin-bottom: 6px;
        }
    </style>
</head>
<body>
    <!-- Print Controls (hidden in print) -->
    <div class="print-controls no-print">
        <button onclick="window.print()" class="btn-print">
            <i class="fas fa-print"></i> Print Report
        </button>
        <a href="/admin/payments" class="btn-back">
            <i class="fas fa-arrow-left"></i> Back to Payments
        </a>
    </div>

    <div class="print-container">
        <!-- Professional Header -->
        <div class="report-header keep-together">
            <img src="{{ url_for('static', filename='images/CV.png') }}" alt="CVBioLabs Logo" class="company-logo">
            <div class="company-name">CVBioLabs</div>
            <div class="company-tagline">For a Healthy Life - Advanced Medical Diagnostics</div>
            <div class="report-title">PAYMENTS MANAGEMENT REPORT</div>
        </div>

        <!-- Report Metadata -->
        <div class="report-meta keep-together">
            <div class="meta-section">
                <h6>Report Information</h6>
                <div class="meta-item">
                    <span class="meta-label">Generated Date:</span>
                    <span class="meta-value">{{ generated_date }}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Generated By:</span>
                    <span class="meta-value">{{ generated_by }}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Total Records:</span>
                    <span class="meta-value">{{ summary.total_transactions }}</span>
                </div>
            </div>
            <div class="meta-section">
                <h6>Filter Criteria</h6>
                <div class="meta-item">
                    <span class="meta-label">Date From:</span>
                    <span class="meta-value">{{ date_range.from }}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Date To:</span>
                    <span class="meta-value">{{ date_range.to }}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Status Filter:</span>
                    <span class="meta-value">{{ filters.status }}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Method Filter:</span>
                    <span class="meta-value">{{ filters.method }}</span>
                </div>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="summary-cards keep-together">
            <div class="summary-card">
                <h6>Total Amount</h6>
                <div class="summary-value">₹{{ "{:,.2f}".format(summary.total_amount) }}</div>
            </div>
            <div class="summary-card">
                <h6>Paid Amount</h6>
                <div class="summary-value">₹{{ "{:,.2f}".format(summary.paid_amount) }}</div>
            </div>
            <div class="summary-card">
                <h6>Pending Amount</h6>
                <div class="summary-value">₹{{ "{:,.2f}".format(summary.pending_amount) }}</div>
            </div>
        </div>

        <!-- Payments Data Table -->
        <div class="table-section">
            <table class="payments-table">
                <thead>
                    <tr>
                        <th style="width: 13%;" class="text-left">Transaction ID</th>
                        <th style="width: 20%;" class="text-left">Customer</th>
                        <th style="width: 16%;" class="text-left">Test</th>
                        <th style="width: 10%;" class="text-right">Amount</th>
                        <th style="width: 12%;" class="text-center">Date</th>
                        <th style="width: 8%;" class="text-center">Status</th>
                        <th style="width: 10%;" class="text-left">Method</th>
                        <th style="width: 8%;" class="text-right">Refund</th>
                        <th style="width: 3%;" class="text-center">Contact</th>
                    </tr>
                </thead>
                <tbody>
                    {% for payment in payments %}
                    <tr>
                        <td class="text-left" style="font-family: 'Courier New', monospace; font-size: 7px; font-weight: 500;">
                            {{ payment.transaction_id or 'N/A' }}
                        </td>
                        <td class="text-left">
                            <div style="font-weight: 600; margin-bottom: 1px; font-size: 8px;">{{ payment.customer_name }}</div>
                            <div style="font-size: 6px; color: #666;">{{ payment.customer_email }}</div>
                        </td>
                        <td class="text-left">
                            <div style="font-weight: 500; font-size: 8px;">{{ payment.test_name }}</div>
                            <div style="font-size: 6px; color: #666;">{{ payment.test_code }}</div>
                        </td>
                        <td class="amount-cell">{{ payment.formatted_amount }}</td>
                        <td class="text-center" style="font-size: 7px;">{{ payment.formatted_date }}</td>
                        <td class="text-center">
                            <span class="status-badge status-{{ payment.payment_status }}">
                                {{ payment.payment_status.title() }}
                            </span>
                        </td>
                        <td class="text-left" style="font-weight: 500; font-size: 8px;">{{ payment.payment_method }}</td>
                        <td class="amount-cell">
                            {% if payment.refund_amount and payment.refund_amount > 0 %}
                                {{ payment.formatted_refund }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-center" style="font-size: 6px;">{{ payment.customer_phone or 'N/A' }}</td>
                    </tr>
                    {% endfor %}

                    {% if not payments %}
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 30px; color: #666; font-style: italic;">
                            No payment records found for the selected criteria.
                            <br><small style="margin-top: 10px; display: block;">
                                Try adjusting your filter criteria or date range to view payment data.
                            </small>
                        </td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>

        <!-- Summary Footer -->
        <div class="summary-cards keep-together" style="margin-top: 16px;">
            <div class="summary-card">
                <h6>Failed Amount</h6>
                <div class="summary-value" style="color: #dc3545;">₹{{ "{:,.2f}".format(summary.failed_amount) }}</div>
            </div>
            <div class="summary-card">
                <h6>Total Refunds</h6>
                <div class="summary-value" style="color: #17a2b8;">₹{{ "{:,.2f}".format(summary.total_refunds) }}</div>
            </div>
            <div class="summary-card">
                <h6>Net Revenue</h6>
                <div class="summary-value" style="color: #28a745;">₹{{ "{:,.2f}".format(summary.paid_amount - summary.total_refunds) }}</div>
            </div>
        </div>

        <!-- Professional Footer -->
        <div class="report-footer keep-together">
            <div class="footer-note">
                This report was generated automatically by CVBioLabs Payment Management System.
            </div>
            <div class="footer-contact">
                CVBioLabs | H.No-1624, Old MIG, Phase-II, BHEL, Seri Lingampally, Hyderabad-500032 |
                Phone: +91 78936 20683 | Email: <EMAIL> | Website: www.cvbiolabs.com
            </div>
            <div style="margin-top: 15px; font-size: 9px; color: #999;">
                ** END OF REPORT **
            </div>
        </div>
    </div>

    <!-- Font Awesome for icons (screen only) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/js/all.min.js" class="no-print"></script>

    <!-- Auto-print functionality (optional) -->
    <script class="no-print">
        // Optional: Auto-print when page loads (uncomment if needed)
        // window.addEventListener('load', function() {
        //     setTimeout(function() {
        //         window.print();
        //     }, 1000);
        // });

        // Keyboard shortcut for printing
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>
</body>
</html>
