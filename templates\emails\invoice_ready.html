{% extends "emails/base.html" %}

{% block title %}Your Invoice is Ready - CVBioLabs{% endblock %}

{% block content %}
<div class="greeting">
    Dear {{ customer_name }},
</div>

<div class="content-text">
    Thank you for choosing CVBioLabs for your medical diagnostic needs! Your payment invoice has been generated and is now ready for download.
</div>

<div class="success-box" style="text-align: center; background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%); border: 2px solid #28a745; border-radius: 12px;">
    <div style="font-size: 20px; color: #155724; margin-bottom: 15px; font-weight: 600;">
        <i class="fas fa-file-invoice-dollar" style="color: #28a745; font-size: 28px; margin-right: 10px;"></i>
        Invoice Ready for Download
    </div>
    <div style="font-size: 16px; color: #155724; margin-bottom: 10px;">
        <strong>Invoice #{{ invoice_number }}</strong>
    </div>
    <div style="font-size: 32px; font-weight: bold; color: #28a745; margin: 15px 0;">
        ₹{{ "%.2f"|format(total_amount) }}
    </div>
    <div style="font-size: 14px; color: #155724;">
        Generated on: {{ invoice_date.strftime('%B %d, %Y') }}
    </div>
</div>

<div class="content-text">
    <strong>Invoice Summary:</strong>
</div>

<div style="background: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 2px solid #002f6c; margin-bottom: 15px;">
        <div style="font-weight: 600; color: #002f6c; font-size: 16px;">Test Details</div>
        <div style="font-weight: 600; color: #f47c20; font-size: 16px;">Amount</div>
    </div>
    
    {% for test in tests %}
    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #e9ecef;">
        <div>
            <div style="font-weight: 600; color: #002f6c;">{{ test.name }}</div>
            {% if test.code %}
            <div style="font-size: 12px; color: #666;">Code: {{ test.code }}</div>
            {% endif %}
            {% if test.quantity and test.quantity > 1 %}
            <div style="font-size: 12px; color: #666;">Quantity: {{ test.quantity }}</div>
            {% endif %}
        </div>
        <div style="font-weight: 600; color: #f47c20;">
            ₹{{ "%.2f"|format(test.amount * (test.quantity or 1)) }}
        </div>
    </div>
    {% endfor %}
    
    <div style="margin-top: 15px; padding-top: 15px; border-top: 2px solid #002f6c;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
            <div style="font-weight: 600; color: #002f6c;">Subtotal:</div>
            <div style="font-weight: 600; color: #002f6c;">₹{{ "%.2f"|format(subtotal) }}</div>
        </div>
        
        {% if tax_amount and tax_amount > 0 %}
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
            <div style="color: #666;">Tax:</div>
            <div style="color: #666;">₹{{ "%.2f"|format(tax_amount) }}</div>
        </div>
        {% endif %}
        
        <div style="display: flex; justify-content: space-between; align-items: center; font-size: 18px; font-weight: bold; color: #28a745; padding-top: 10px; border-top: 1px solid #e9ecef;">
            <div>Total Amount:</div>
            <div>₹{{ "%.2f"|format(total_amount) }}</div>
        </div>
    </div>
</div>



<div class="info-box">
    <div style="font-weight: 600; margin-bottom: 10px;">
        <i class="fas fa-credit-card"></i> Payment Information
    </div>
    <div style="margin-bottom: 15px;">
        <div><strong>Payment Status:</strong> <span style="color: {% if payment_status == 'paid' %}#28a745{% elif payment_status == 'pending' %}#ffc107{% else %}#dc3545{% endif %};">{{ payment_status.title() }}</span></div>
        {% if payment_method %}
        <div><strong>Payment Method:</strong> {{ payment_method }}</div>
        {% endif %}
        {% if transaction_id %}
        <div><strong>Transaction ID:</strong> {{ transaction_id }}</div>
        {% endif %}
        {% if payment_date %}
        <div><strong>Payment Date:</strong> {{ payment_date.strftime('%B %d, %Y at %I:%M %p') }}</div>
        {% endif %}
    </div>
    
    {% if booking_id %}
    <div style="margin-bottom: 15px;">
        <strong>Booking Reference:</strong> {{ booking_id }}
    </div>
    {% endif %}
    
    {% if due_date and payment_status != 'paid' %}
    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 10px; margin-top: 10px;">
        <div style="color: #856404; font-weight: 600;">
            <i class="fas fa-exclamation-triangle"></i> Payment Due Date: {{ due_date.strftime('%B %d, %Y') }}
        </div>
    </div>
    {% endif %}
</div>

<div class="info-box">
    <div style="font-weight: 600; margin-bottom: 10px;">
        <i class="fas fa-info-circle"></i> How to Access Your Invoice
    </div>
    <ol style="margin: 0; padding-left: 20px;">
        <li>Click the "Download Invoice" button above</li>
        <li>The invoice will be downloaded as a PDF file</li>
        <li>You can save, print, or share the invoice as needed</li>
        <li>Keep this invoice for your records and tax purposes</li>
    </ol>
</div>

{% if payment_status != 'paid' %}
<div class="info-box" style="background: #e8f4fd; border: 1px solid #bee5eb;">
    <div style="font-weight: 600; margin-bottom: 10px; color: #0c5460;">
        <i class="fas fa-money-bill-wave"></i> Payment Options
    </div>
    <div style="color: #0c5460;">
        If you haven't completed your payment yet, you can pay online through our secure payment gateway or visit our center for cash payment. For any payment assistance, please contact our support team.
    </div>
</div>
{% endif %}

<div class="content-text">
    <strong>Need Help?</strong>
</div>

<div class="content-text">
    <ul style="padding-left: 20px; color: #333;">
        <li><strong>Email:</strong> <a href="mailto:<EMAIL>" style="color: #f47c20; text-decoration: none;"><EMAIL></a></li>
        <li><strong>Phone:</strong> +91 78936 20683 (Available 24/7)</li>
        <li><strong>WhatsApp:</strong> +91 78936 20683</li>
        <li><strong>Live Chat:</strong> Available on our website</li>
    </ul>
</div>

<div class="content-text" style="margin-top: 30px;">
    Thank you for choosing CVBioLabs for your healthcare needs. We're committed to providing you with accurate results and exceptional service.
</div>

<div class="content-text" style="color: #002f6c; font-weight: 600;">
    Best regards,<br>
    The CVBioLabs Team
</div>
{% endblock %}
