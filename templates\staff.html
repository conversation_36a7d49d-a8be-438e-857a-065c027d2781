<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CV BIOLABS - Staff Dashboard</title>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-orange: #f58220;
            --deep-blue: #003865;
            --bright-blue: #007dc5;
            --light-bg: #f0f7fb;
            --white: #ffffff;
            --text-dark: #1a1a1a;
            --text-light: #6b7280;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --gradient-primary: linear-gradient(135deg, var(--deep-blue) 0%, var(--bright-blue) 100%);
            --gradient-accent: linear-gradient(135deg, var(--primary-orange) 0%, #ff6b35 100%);
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            overflow-x: hidden;
            background: var(--light-bg);
            opacity: 0;
            animation: fadeInBody 0.5s ease-out forwards;
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-orange);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #e07020;
        }

        @keyframes fadeInBody {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        /* Loading Spinner */
        .loading-spinner {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
            display: none;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--text-light);
            border-top: 4px solid var(--primary-orange);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--orange);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #e07020;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px) saturate(180%);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            z-index: 1000;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            animation: slideDown 0.6s ease-out;
        }

        .header.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 100%;
            padding: 0 2rem;
            max-width: 100%;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: var(--text-dark);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .logo img {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            filter: drop-shadow(0 4px 8px rgba(245, 130, 32, 0.3));
            transition: all 0.3s ease;
        }

        .logo-text {
            font-family: 'Poppins', sans-serif;
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--primary-orange);
            text-shadow: 0 2px 4px rgba(245, 130, 32, 0.2);
        }

        .mobile-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--primary-orange);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .mobile-toggle:hover {
            background: rgba(245, 130, 32, 0.1);
            transform: scale(1.1);
        }

        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: var(--text-dark);
            font-weight: 500;
            padding: 0.5rem 1rem;
            background: rgba(245, 130, 32, 0.05);
            border-radius: 25px;
            border: 1px solid rgba(245, 130, 32, 0.1);
            transition: all 0.3s ease;
        }

        .user-info:hover {
            background: rgba(245, 130, 32, 0.1);
            transform: translateY(-2px);
        }

        .user-info i {
            font-size: 1.5rem;
            color: var(--primary-orange);
        }

        .logout-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--gradient-accent);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
        }

        .logout-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .logout-btn:hover::before {
            left: 100%;
        }

        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Left Sidebar Navigation */
        .sidebar {
            position: fixed;
            top: 80px;
            left: 0;
            width: 280px;
            height: calc(100vh - 80px);
            background: linear-gradient(180deg, var(--white) 0%, rgba(248, 249, 250, 0.95) 100%);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(245, 130, 32, 0.1);
            box-shadow: 4px 0 20px rgba(245, 130, 32, 0.1);
            z-index: 999;
            overflow-y: auto;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            animation: slideInLeft 0.6s ease-out;
        }

        .nav-links {
            display: flex;
            flex-direction: column;
            list-style: none;
            gap: 0.5rem;
            padding: 2rem 1rem;
        }

        .nav-links li {
            margin: 0;
        }

        .nav-links a {
            color: var(--text-dark);
            text-decoration: none;
            font-weight: 500;
            position: relative;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 1rem;
            white-space: nowrap;
            font-size: 0.95rem;
            width: 100%;
            overflow: hidden;
        }

        .nav-links a::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 4px;
            background: var(--gradient-accent);
            transform: scaleY(0);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(245, 130, 32, 0.1), transparent);
            transition: left 0.5s;
        }

        .nav-links a:hover::after {
            left: 100%;
        }

        .nav-links a i {
            font-size: 1.2rem;
            color: var(--primary-orange);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 20px;
            text-align: center;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: rgba(245, 130, 32, 0.1);
            color: var(--primary-orange);
            transform: translateX(8px);
            box-shadow: 0 4px 20px rgba(245, 130, 32, 0.15);
        }

        .nav-links a:hover::before,
        .nav-links a.active::before {
            transform: scaleY(1);
        }

        .nav-links a:hover i,
        .nav-links a.active i {
            color: var(--primary-orange);
            transform: scale(1.2) rotate(5deg);
        }

        .nav-links a span {
            color: var(--text-dark);
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-links a:hover span,
        .nav-links a.active span {
            color: var(--primary-orange);
            font-weight: 600;
        }

        /* Animations */
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-100%);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Mobile Navigation */
        .mobile-nav {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(180deg,
                rgba(255, 255, 255, 0.98) 0%,
                rgba(248, 249, 250, 0.95) 50%,
                rgba(245, 245, 247, 0.92) 100%);
            backdrop-filter: blur(25px) saturate(180%);
            border-top: 2px solid rgba(245, 130, 32, 0.2);
            padding: 0.8rem 0.5rem 1.2rem 0.5rem;
            z-index: 1000;
            box-shadow:
                0 -8px 32px rgba(245, 130, 32, 0.15),
                0 -2px 8px rgba(0, 0, 0, 0.05);
            animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .mobile-nav::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 4px;
            background: linear-gradient(90deg, transparent, rgba(245, 130, 32, 0.3), transparent);
            border-radius: 2px;
        }

        .mobile-nav-menu {
            display: flex;
            justify-content: space-around;
            align-items: center;
            list-style: none;
            margin: 0;
            padding: 0;
            max-width: 500px;
            margin: 0 auto;
        }

        .mobile-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.3rem;
            padding: 0.6rem 0.4rem;
            text-decoration: none;
            color: #6c757d;
            font-size: 0.7rem;
            font-weight: 600;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 16px;
            min-width: 55px;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            background: transparent;
        }

        .mobile-nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(245, 130, 32, 0.1), rgba(245, 130, 32, 0.05));
            transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 16px;
        }

        .mobile-nav-item:active::before {
            left: 100%;
        }

        .mobile-nav-item:hover,
        .mobile-nav-item.active {
            color: var(--primary-orange);
            background: linear-gradient(135deg, rgba(245, 130, 32, 0.12), rgba(245, 130, 32, 0.08));
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 4px 16px rgba(245, 130, 32, 0.2);
        }

        .mobile-nav-item i {
            font-size: 1.3rem;
            margin-bottom: 0.2rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            color: #6c757d;
        }

        .mobile-nav-item:hover i,
        .mobile-nav-item.active i {
            transform: scale(1.15) rotate(5deg);
            color: var(--primary-orange);
            filter: drop-shadow(0 2px 4px rgba(245, 130, 32, 0.3));
        }

        .mobile-nav-item span {
            font-weight: 600;
            text-align: center;
            line-height: 1.1;
            letter-spacing: 0.02em;
            transition: all 0.3s ease;
        }

        .mobile-nav-item:hover span,
        .mobile-nav-item.active span {
            color: var(--primary-orange);
            transform: scale(1.05);
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(100%);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Touch-friendly mobile navigation */
        @media (hover: none) and (pointer: coarse) {
            .mobile-nav-item {
                padding: 0.8rem 0.5rem;
                min-height: 60px;
            }

            .mobile-nav-item:active {
                transform: translateY(-2px) scale(0.98);
                background: linear-gradient(135deg, rgba(245, 130, 32, 0.2), rgba(245, 130, 32, 0.1));
            }
        }

        /* Enhanced mobile navigation for better UX */
        .mobile-nav-item {
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }

        .mobile-nav-item:focus {
            outline: 2px solid var(--primary-orange);
            outline-offset: 2px;
        }

        /* Main Content */
        .main-content {
            margin-left: 280px;
            padding: 100px 2rem 2rem 2rem;
            min-height: 100vh;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Dashboard Cards */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(245, 130, 32, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(245, 130, 32, 0.05);
            min-height: 180px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            opacity: 0;
            transform: translateY(20px);
            animation: slideInUp 0.6s ease-out forwards;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-accent);
        }

        .card::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(245, 130, 32, 0.05), transparent);
            transition: left 0.5s;
        }

        .card:hover::after {
            left: 100%;
        }

        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(245, 130, 32, 0.15);
        }

        .card:nth-child(1) { animation-delay: 0.1s; }
        .card:nth-child(2) { animation-delay: 0.2s; }
        .card:nth-child(3) { animation-delay: 0.3s; }
        .card:nth-child(4) { animation-delay: 0.4s; }
        .card:nth-child(5) { animation-delay: 0.5s; }
        .card:nth-child(6) { animation-delay: 0.6s; }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid rgba(245, 130, 32, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .header-title {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .card-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin: 0;
        }

        .card-title i {
            color: var(--orange);
            font-size: 1.5rem;
        }

        .card-subtitle {
            color: var(--text-light);
            font-size: 0.9rem;
            margin: 0;
            font-weight: 400;
        }

        .header-actions {
            display: flex;
            gap: 0.75rem;
            align-items: center;
        }

        .card-title {
            font-family: 'Poppins', sans-serif;
            color: var(--text-dark) !important;
            font-size: 1.3rem;
            font-weight: 600;
            margin: 0;
            opacity: 1 !important;
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: var(--gradient-primary);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 4px 12px rgba(245, 130, 32, 0.3);
            transition: all 0.3s ease;
        }

        .card:hover .card-icon {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(245, 130, 32, 0.4);
        }

        .card-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--orange) !important;
            margin-bottom: 0.5rem;
            font-family: 'Poppins', sans-serif;
            opacity: 1 !important;
        }

        .card-description {
            color: var(--text-dark) !important;
            font-size: 1rem;
            font-weight: 500;
            opacity: 0.8;
        }

        /* Ensure all dashboard card text is visible */
        .dashboard-grid .card * {
            color: inherit !important;
            opacity: 1 !important;
        }

        .dashboard-grid .card .card-title {
            color: var(--text-dark) !important;
        }

        .dashboard-grid .card .card-value {
            color: var(--orange) !important;
        }

        .dashboard-grid .card .card-description {
            color: var(--text-dark) !important;
        }


        /* Forms */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.75rem;
            color: var(--text-dark);
            font-weight: 600;
            font-size: 0.95rem;
        }

        .form-control {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--orange);
            box-shadow: 0 0 0 3px rgba(245, 130, 32, 0.1);
        }

        .form-control:hover {
            border-color: #d1d5db;
        }

        /* Form Buttons */
        .form-buttons {
    display: flex;
    justify-content: center;
    align-items: center; /* Optional: vertically center if container has height */
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
    text-align: center;
    width: 100%; /* Ensure full width */
}

        /* Required Field Indicator */
        .required::after {
            content: " *";
            color: var(--danger);
        }

        /* Tables */
        .table-responsive {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: var(--shadow-md);
            margin-top: 1.5rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 1.25rem 1rem;
            text-align: left;
            border-bottom: 1px solid #f1f5f9;
        }

        th {
            background: var(--light-bg);
            color: var(--text-dark);
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        td {
            color: var(--text-dark);
            font-size: 0.95rem;
        }

        tbody tr:hover {
            background: rgba(245, 130, 32, 0.05);
        }

        /* Enhanced Search and Filter Section */
        .search-filter-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.95) 100%);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(245, 130, 32, 0.1);
            box-shadow: 0 8px 32px rgba(245, 130, 32, 0.08);
            animation: slideInUp 0.6s ease-out;
        }

        .search-container {
            margin-bottom: 2rem;
        }

        .search-input-wrapper {
            position: relative;
            max-width: 600px;
            margin: 0 auto;
        }

        .search-icon {
            position: absolute;
            left: 1.25rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary-orange);
            font-size: 1.2rem;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .search-input {
            width: 100%;
            padding: 1rem 3.5rem 1rem 3.5rem !important;
            border-radius: 50px !important;
            border: 2px solid rgba(245, 130, 32, 0.2) !important;
            font-size: 1rem;
            height: 56px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: white;
            box-shadow: 0 4px 20px rgba(245, 130, 32, 0.05);
            font-weight: 500;
        }

        .search-input:focus {
            border-color: var(--primary-orange) !important;
            box-shadow: 0 0 0 4px rgba(245, 130, 32, 0.1), 0 8px 25px rgba(245, 130, 32, 0.15) !important;
            transform: translateY(-2px);
        }

        .search-input:focus + .search-icon {
            color: var(--primary-orange);
            transform: translateY(-50%) scale(1.1);
        }

        .clear-search {
            position: absolute;
            right: 1.25rem;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(245, 130, 32, 0.1);
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-orange);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .clear-search:hover {
            background: var(--primary-orange);
            color: white;
            transform: translateY(-50%) scale(1.1);
        }

        .search-input:focus {
            border-color: var(--orange) !important;
            box-shadow: 0 0 0 0.2rem rgba(245, 130, 32, 0.15) !important;
            outline: none;
        }

        .clear-search {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .clear-search:hover {
            background: var(--primary-orange);
            color: white;
            transform: translateY(-50%) scale(1.1);
        }

        .filter-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            align-items: end;
            margin-top: 1rem;
            justify-content: flex-start;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            position: relative;
            min-width: 200px;
            flex: 0 0 auto;
        }

        .filter-label {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .filter-label::before {
            content: '';
            width: 4px;
            height: 4px;
            background: var(--primary-orange);
            border-radius: 50%;
        }

        .filter-select,
        .filter-date {
            padding: 1rem 1.25rem;
            border: 2px solid rgba(245, 130, 32, 0.2);
            border-radius: 16px;
            font-size: 0.95rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: white;
            color: var(--text-dark);
            font-weight: 500;
            box-shadow: 0 4px 20px rgba(245, 130, 32, 0.05);
            appearance: none;
            cursor: pointer;
            position: relative;
        }

        .filter-select {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f58220' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 1rem center;
            background-size: 1.2em;
            padding-right: 3rem;
        }

        .filter-select:focus,
        .filter-date:focus {
            border-color: var(--primary-orange);
            outline: none;
            box-shadow: 0 0 0 4px rgba(245, 130, 32, 0.1), 0 8px 25px rgba(245, 130, 32, 0.15);
            transform: translateY(-2px);
        }

        .filter-select:hover,
        .filter-date:hover {
            border-color: var(--primary-orange);
            transform: translateY(-1px);
            box-shadow: 0 6px 25px rgba(245, 130, 32, 0.1);
        }

        .filter-clear {
            background: var(--gradient-accent);
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 0.95rem;
            box-shadow: 0 4px 20px rgba(245, 130, 32, 0.2);
            position: relative;
            overflow: hidden;
        }

        .filter-clear::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .filter-clear:hover::before {
            left: 100%;
        }

        .filter-clear:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(245, 130, 32, 0.3);
        }

        .filter-clear:active {
            transform: translateY(0);
        }

        /* Enhanced visual hierarchy */
        .search-filter-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            border-radius: 20px 20px 0 0;
        }

        .search-input::placeholder {
            color: rgba(0, 0, 0, 0.4);
            font-weight: 400;
        }

        .filter-select option {
            padding: 0.75rem;
            font-weight: 500;
        }

        /* Smooth animations */
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        /* Focus states for accessibility */
        .search-input:focus,
        .filter-select:focus,
        .filter-date:focus {
            outline: 2px solid var(--primary-orange);
            outline-offset: 2px;
        }

        /* Loading state for filters */
        .filter-loading {
            position: relative;
            pointer-events: none;
        }

        .filter-loading::after {
            content: '';
            position: absolute;
            top: 50%;
            right: 1rem;
            width: 16px;
            height: 16px;
            border: 2px solid var(--primary-orange);
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Tablet responsive layout */
        @media (max-width: 1024px) and (min-width: 769px) {
            .filter-container {
                gap: 1rem;
            }

            .filter-group {
                min-width: 180px;
                flex: 1 1 calc(50% - 0.5rem);
            }

            .search-input-wrapper {
                max-width: 500px;
            }
        }

        .filter-clear {
            border-radius: 8px !important;
            padding: 0.75rem 1rem !important;
            font-size: 0.9rem;
            height: fit-content;
            align-self: flex-end;
            margin-top: 1.5rem;
        }

        /* Legacy support for old search-box and filter-box */
        .search-box {
            flex: 1;
            min-width: 250px;
        }

        .filter-box {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .filter-box .form-control {
            min-width: 150px;
        }

        /* Buttons */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 0.9rem;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--gradient-accent);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning) 0%, #d97706 100%);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-info {
            background: linear-gradient(135deg, var(--bright-blue) 0%, #0ea5e9 100%);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn i {
            font-size: 1rem;
        }

        /* Small Button Variant */
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            border-radius: 25px;
        }

        /* Status Badges */
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.85rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
        }

        .status-scheduled {
            background: rgba(0, 125, 197, 0.1);
            color: var(--bright-blue);
            border: 1px solid rgba(0, 125, 197, 0.2);
        }

        .status-in-progress,
        .status-in-transit {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .status-completed {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-cancelled {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .status-paid {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-pending {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .status-failed {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(8px);
            z-index: 1050;
            animation: fadeIn 0.3s ease;
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            padding: 2.5rem;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            max-height: 85vh;
            overflow-y: auto;
            box-shadow: var(--shadow-xl);
            position: relative;
            animation: slideInUp 0.3s ease;
        }

        .modal-lg {
            max-width: 900px;
        }

        .close {
            position: absolute;
            top: 1rem;
            right: 1.5rem;
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
            color: var(--text-light);
            transition: color 0.3s ease;
        }

        .close:hover {
            color: var(--danger);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }



        /* Mobile Toggle Button */
        .mobile-toggle {
            display: none;
            background: var(--gradient-primary);
            border: none;
            color: white;
            padding: 0.75rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .mobile-toggle:hover {
            transform: scale(1.05);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .mobile-toggle {
                display: block;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .mobile-nav {
                display: block !important;
            }

            .main-content {
                margin-left: 0;
                padding: 100px 1rem 100px 1rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .card {
                padding: 1.5rem;
                min-height: 160px;
            }

            .card-title {
                font-size: 1.1rem !important;
            }

            .card-value {
                font-size: 2rem !important;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
            }

            .nav {
                padding: 1rem;
            }

            .logo img {
                width: 50px;
                height: 50px;
            }

            .logo-text {
                font-size: 1.2rem;
            }

            .auth-buttons {
                flex-direction: row;
                gap: 0.5rem;
            }

            .user-info {
                padding: 0.4rem 0.8rem;
                font-size: 0.85rem;
            }

            .user-info span {
                display: none;
            }

            .logout-btn {
                padding: 0.5rem 1rem !important;
                font-size: 0.8rem !important;
            }

            .logout-btn span {
                display: none;
            }

            .btn {
                padding: 0.65rem 1.25rem;
                font-size: 0.85rem;
            }

            .form-control {
                padding: 0.75rem 1rem;
            }

            .search-filter-section {
                padding: 1.5rem;
            }

            .filter-container {
                flex-direction: column;
                gap: 1rem;
            }

            .filter-group {
                min-width: 100%;
                flex: 1 1 100%;
            }

            .table-responsive {
                font-size: 0.85rem;
            }

            th, td {
                padding: 0.75rem 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .nav {
                padding: 0.75rem;
            }

            .auth-buttons {
                flex-direction: column;
                gap: 0.25rem;
                align-items: flex-end;
            }

            .card {
                padding: 1rem;
            }

            .card-title {
                font-size: 1rem;
            }

            .card-value {
                font-size: 1.75rem;
            }

            .btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.75rem;
            }

            .mobile-nav-item {
                font-size: 0.65rem;
                min-width: 48px;
                padding: 0.5rem 0.3rem;
                gap: 0.25rem;
            }

            .mobile-nav-item i {
                font-size: 1.1rem;
            }

            .mobile-nav-item span {
                font-size: 0.6rem;
                font-weight: 600;
            }
        }

        /* Payment and Report Specific Styles */
        .report-section {
            padding: 1.5rem;
            background-color: white;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }

        .report-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .report-stats p {
            padding: 1rem;
            background-color: var(--light-blue);
            border-radius: 4px;
            text-align: center;
        }

        .report-details {
            padding: 1.5rem;
            background-color: #f8f9fa;
            border-radius: 4px;
            margin-bottom: 1.5rem;
        }

        .report-details p {
            margin-bottom: 0.5rem;
        }

        .report-details strong {
            color: #004085;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .action-buttons .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-badge.pending {
            background-color: #ffc107;
            color: #000;
        }

        .status-badge.completed {
            background-color: #28a745;
            color: #fff;
        }

        .status-badge.reviewed {
            background-color: #17a2b8;
            color: #fff;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .header-actions .btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .header-actions .btn i {
            font-size: 1.1rem;
            margin-right: 0.5rem;
        }

        .header-actions .btn:hover i {
            transform: scale(1.1);
        }

        /* Responsive styles for reports section */
        @media (max-width: 768px) {
            .report-stats {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .action-buttons .btn {
                width: 100%;
            }

            .header-actions {
                flex-direction: column;
                width: 100%;
            }

            .header-actions .btn {
                width: 100%;
            }
        }

        /* Alert */
        .alert {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem;
            border-radius: 4px;
            z-index: 1050;
            display: none;
        }

        .alert-success {
            background-color: var(--success);
            color: white;
        }

        .alert-danger {
            background-color: var(--danger);
            color: white;
        }

        .alert.active {
            display: block;
        }

        .tab-pane {
    display: none; /* Hide all tab panes by default */
}

.tab-pane.active {
    display: block; /* Show only the active tab pane */
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}


    /* Container for the report buttons */
.report-buttons-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 20px;
  margin-bottom: 20px;
}

/* Base button styles */
.btn {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
  flex: 1 0 auto;
  min-width: 120px;
  max-width: 200px;
}

/* Button color variations - Updated to match theme */
.btn-primary {
  background: var(--gradient-primary);
  color: white;
  width: fit-content;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

/* Hover effects */
.btn:hover {
  opacity: 0.9;
}

/* Search and Filter Section Styles */
.search-filter-section {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 1rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.search-box {
    flex: 1;
    min-width: 200px;
}

.filter-box {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-box select {
    min-width: 150px;
}

/* Responsive Table Styles */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin: 0;
    padding: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-responsive table {
    width: 100%;
    min-width: 1000px;
    border-collapse: separate;
    border-spacing: 0;
}

.table-responsive th {
    position: sticky;
    top: 0;
    background-color: var(--light-blue);
    z-index: 1;
    white-space: nowrap;
    padding: 1rem;
    font-weight: 600;
}

.table-responsive td {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    white-space: nowrap;
}

.table-responsive tr:hover {
    background-color: #f8f9fa;
}

/* Action Buttons Container */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-start;
    min-width: 160px;
}

.action-buttons .btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
    white-space: nowrap;
}

@media (max-width: 768px) {
    .search-filter-section {
        flex-direction: column;
    }
    
    .search-box,
    .filter-box {
        width: 100%;
    }
    
    .filter-box select {
        width: 100%;
    }

    .filter-container {
        flex-direction: column;
        gap: 1rem;
    }

    .filter-group {
        min-width: 100%;
        flex: 1 1 100%;
    }

    .filter-clear {
        margin-top: 1rem;
        align-self: stretch;
    }

    .table-responsive {
        margin: 0 -1rem;
    }
    
    .table-responsive table {
        font-size: 0.9rem;
    }
    
    .table-responsive th,
    .table-responsive td {
        padding: 0.5rem;
    }
    
    .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}

    /* Improved Modal Styles */
    .modal-content {
        max-width: 800px;
        width: 95%;
        max-height: 90vh;
        overflow-y: auto;
        padding: 2rem;
    }

    .modal-content h2 {
        color: var(--dark-blue);
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--light-blue);
    }

    .modal-form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        color: var(--dark-blue);
        font-weight: 500;
    }

    .form-control {
        width: 100%;
        padding: 0.8rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        transition: border-color 0.3s ease;
    }

    .form-control:focus {
        border-color: var(--dark-blue);
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 47, 108, 0.1);
    }

    .form-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #eee;
    }

    /* Search and Filter Improvements */
    .search-box input {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 0.8rem;
        width: 100%;
        transition: border-color 0.3s ease;
    }

    .search-box input:focus {
        border-color: var(--dark-blue);
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 47, 108, 0.1);
    }

    .filter-box select {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 0.8rem;
        background-color: white;
        transition: border-color 0.3s ease;
    }

    .filter-box select:focus {
        border-color: var(--dark-blue);
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 47, 108, 0.1);
    }

    @media (max-width: 768px) {
        .modal-content {
            padding: 1rem;
            margin: 1rem;
        }

        .modal-form-grid {
            grid-template-columns: 1fr;
        }

        .form-buttons {
            flex-direction: column;
        }

        .form-buttons .btn {
            width: 100%;
        }

        .action-buttons {
            flex-direction: column;
            min-width: 120px;
        }

        .action-buttons .btn {
            width: 100%;
        }
    }

    /* Add these styles to the existing CSS */
    .table-responsive {
        max-height: calc(100vh - 300px);
        overflow-y: auto;
    }

    .table-responsive table {
        font-size: 0.9rem;
    }

    .table-responsive th,
    .table-responsive td {
        padding: 0.5rem;
        white-space: nowrap;
    }

    .search-filter-section {
        padding: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .search-box input,
    .filter-box select {
        padding: 0.4rem;
        font-size: 0.9rem;
    }

    .card-header {
        padding: 0.5rem 1rem;
    }

    .card-title {
        font-size: 1.1rem;
        margin: 0;
    }

    .patients-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 1.5rem;
        padding: 1rem;
    }

    .patient-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        padding: 1.5rem;
    }

    .patient-card h4 {
        color: var(--dark-blue);
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid var(--light-blue);
    }

    .patient-info {
        margin-bottom: 1.5rem;
    }

    .patient-info p {
        margin: 0.5rem 0;
        display: flex;
        justify-content: space-between;
    }

    .patient-info strong {
        color: var(--dark-blue);
    }

    .booking-history {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #eee;
    }

    .booking-history h5 {
        color: var(--dark-blue);
        margin-bottom: 0.5rem;
    }

    .booking-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px dashed #eee;
    }

    .booking-item:last-child {
        border-bottom: none;
    }

    .patient-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #eee;
    }

    .edit-field {
        display: none;
        margin-top: 0.5rem;
    }

    .edit-field.active {
        display: block;
    }

    @media (max-width: 768px) {
        .patients-grid {
            grid-template-columns: 1fr;
        }
    }

    /* View Collection Details Modal */
    .modal#viewCollectionDetailsModal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.6);
        backdrop-filter: blur(8px);
        z-index: 1001;
        animation: fadeIn 0.3s ease;
    }

    .modal#viewCollectionDetailsModal.active {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal#viewCollectionDetailsModal .modal-content {
        background: white;
        border-radius: 20px;
        padding: 2.5rem;
        width: 90%;
        max-width: 800px;
        max-height: 85vh;
        overflow-y: auto;
        box-shadow: var(--shadow-xl);
        position: relative;
        animation: slideInUp 0.3s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .modal#viewCollectionDetailsModal .close {
        position: absolute;
        top: 1rem;
        right: 1.5rem;
        font-size: 1.5rem;
        font-weight: bold;
        cursor: pointer;
        color: var(--text-light);
        transition: color 0.3s ease;
        z-index: 10;
    }

    .modal#viewCollectionDetailsModal .close:hover {
        color: var(--danger);
    }

    .collection-details {
        padding: 0;
        margin-top: 1rem;
    }

    .detail-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 16px;
        border: 1px solid rgba(0, 0, 0, 0.05);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .detail-section:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .detail-section h4 {
        color: var(--dark-blue);
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid var(--orange);
        font-family: 'Poppins', sans-serif;
        font-weight: 600;
        font-size: 1.2rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .detail-section h4::before {
        content: '';
        width: 4px;
        height: 20px;
        background: var(--gradient-primary);
        border-radius: 2px;
    }

    .detail-section p {
        margin: 1rem 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        font-size: 0.95rem;
    }

    .detail-section p:last-child {
        border-bottom: none;
    }

    .detail-section strong {
        color: var(--dark-blue);
        min-width: 150px;
        font-weight: 600;
        flex-shrink: 0;
    }

    .detail-section span {
        color: var(--text-dark);
        font-weight: 500;
        text-align: right;
    }

    /* Modal Header Styling */
    .modal#viewCollectionDetailsModal .modal-header {
        text-align: center;
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 2px solid rgba(245, 130, 32, 0.1);
    }

    .modal#viewCollectionDetailsModal .modal-header h2 {
        color: var(--dark-blue);
        font-family: 'Poppins', sans-serif;
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
    }

    .modal#viewCollectionDetailsModal .modal-header h2 i {
        color: var(--orange);
        font-size: 1.6rem;
    }

    .modal#viewCollectionDetailsModal .modal-subtitle {
        color: var(--text-light);
        font-size: 1rem;
        margin: 0;
        font-style: italic;
    }

    /* Enhanced Form Buttons */
    .modal#viewCollectionDetailsModal .form-buttons {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-top: 2.5rem;
        padding-top: 2rem;
        border-top: 2px solid rgba(245, 130, 32, 0.1);
    }

    .modal#viewCollectionDetailsModal .form-buttons .btn {
        padding: 0.875rem 1.75rem;
        font-weight: 600;
        border-radius: 12px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        min-width: 140px;
        justify-content: center;
    }

    .modal#viewCollectionDetailsModal .form-buttons .btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    /* Responsive Design for Collection Details Modal */
    @media (max-width: 768px) {
        .modal#viewCollectionDetailsModal .modal-content {
            width: 95%;
            padding: 1.5rem;
            margin: 2% auto;
        }

        .modal#viewCollectionDetailsModal .modal-header h2 {
            font-size: 1.4rem;
            flex-direction: column;
            gap: 0.5rem;
        }

        .modal#viewCollectionDetailsModal .detail-section {
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .modal#viewCollectionDetailsModal .detail-section h4 {
            font-size: 1rem;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.25rem;
        }

        .modal#viewCollectionDetailsModal .detail-section p {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.25rem;
        }

        .modal#viewCollectionDetailsModal .detail-section strong {
            min-width: auto;
        }

        .modal#viewCollectionDetailsModal .form-buttons {
            flex-direction: column;
            gap: 0.75rem;
        }

        .modal#viewCollectionDetailsModal .form-buttons .btn {
            width: 100%;
            min-width: auto;
        }
    }

    /* Add animation keyframes */
    @keyframes countUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
        100% {
            transform: scale(1);
        }
    }

    .stat-card {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        animation: countUp 0.5s ease-out forwards;
        opacity: 0;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .stat-card .stat-value {
        font-size: 2rem;
        font-weight: bold;
        color: var(--dark-blue);
        margin: 0.5rem 0;
        animation: pulse 2s infinite;
    }

    .stat-card .stat-label {
        color: var(--grey);
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .stat-card .stat-icon {
        font-size: 2rem;
        color: var(--orange);
        margin-bottom: 1rem;
    }

    .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    /* Add animation delays for each card */
    .stat-card:nth-child(1) { animation-delay: 0.1s; }
    .stat-card:nth-child(2) { animation-delay: 0.3s; }
    .stat-card:nth-child(3) { animation-delay: 0.5s; }

    /* View Agent Details Modal */
    .modal#viewAgentDetailsModal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        z-index: 1001;
    }

    .modal#viewAgentDetailsModal.active {
        display: block;
    }

    .modal#viewAgentDetailsModal .modal-content {
        background-color: white;
        margin: 5% auto;
        padding: 2rem;
        border-radius: 8px;
        width: 90%;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
    }

    .modal#viewAgentDetailsModal .close {
        float: right;
        font-size: 1.5rem;
        font-weight: bold;
        cursor: pointer;
    }

    .agent-details {
        padding: 1rem;
    }

    .detail-section {
        margin-bottom: 1.5rem;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 4px;
    }

    .detail-section h4 {
        color: var(--dark-blue);
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #dee2e6;
    }

    .detail-section p {
        margin: 0.5rem 0;
    }

    .detail-section strong {
        color: var (--dark-blue);
        min-width: 150px;
        display: inline-block;
    }

    /* Update Agent Status Modal */
    .modal#updateAgentStatusModal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        z-index: 1001;
    }

    .modal#updateAgentStatusModal.active {
        display: block;
    }

    .modal#updateAgentStatusModal .modal-content {
        background-color: white;
        margin: 5% auto;
        padding: 2rem;
        border-radius: 8px;
        width: 90%;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
    }

    .modal#updateAgentStatusModal .close {
        float: right;
        font-size: 1.5rem;
        font-weight: bold;
        cursor: pointer;
    }

    .update-agent-form {
        padding: 1rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        color: var(--dark-blue);
        font-weight: 500;
    }

    .form-control {
        width: 100%;
        padding: 0.8rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        transition: border-color 0.3s ease;
    }

    .form-control:focus {
        border-color: var(--dark-blue);
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 47, 108, 0.1);
    }

    .form-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #eee;
    }
    </style>
</head>
<body>
    <!-- Loading Spinner -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner"></div>
    </div>

    <!-- Header -->
    <header class="header" id="header">
        <nav class="nav">
            <div class="logo">
                <button class="mobile-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <img src="{{ url_for('static', filename='images/CV.png') }}" alt="CVBIOLABS Logo">
                <span class="logo-text">CVBIOLABS</span>
            </div>
            <div class="auth-buttons">
                <div class="user-info">
                    {% if current_user %}
                        <i class="fas fa-user-circle"></i>
                        <span>Welcome, {{ current_user.name }}</span>
                    {% else %}
                        <i class="fas fa-user-circle"></i>
                        <span>Welcome, nikhil</span>
                    {% endif %}
                </div>
                <a href="/logout" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i> <span>Logout</span>
                </a>
            </div>
        </nav>
    </header>

    <!-- Left Sidebar Navigation -->
    <aside class="sidebar" id="sidebar">
        <ul class="nav-links">
            <li><a href="#dashboard" class="nav-link active" data-tab="dashboard">
                <i class="fas fa-chart-pie"></i> <span>Dashboard</span>
            </a></li>
            <li><a href="#appointments" class="nav-link" data-tab="appointments">
                <i class="fas fa-user-injured"></i> <span>Patient Records</span>
            </a></li>
            <li><a href="#sample-collections" class="nav-link" data-tab="sample-collections">
                <i class="fas fa-vial"></i> <span>Sample Collections</span>
            </a></li>
            <li><a href="#agents" class="nav-link" data-tab="agents">
                <i class="fas fa-truck"></i> <span>Pickup Agents</span>
            </a></li>
            <li><a href="#reports" class="nav-link" data-tab="reports">
                <i class="fas fa-file-medical"></i> <span>Reports</span>
            </a></li>
            <li><a href="#verification-status" class="nav-link" data-tab="verification-status">
                <i class="fas fa-check-circle"></i> <span>Verification Status</span>
            </a></li>
        </ul>
    </aside>

    <!-- Mobile Navigation -->
    <nav class="mobile-nav">
        <ul class="mobile-nav-menu">
            <li>
                <a href="#dashboard" class="mobile-nav-item nav-link active" data-tab="dashboard">
                    <i class="fas fa-chart-pie"></i>
                    <span>Dashboard</span>
                </a>
            </li>
            <li>
                <a href="#appointments" class="mobile-nav-item nav-link" data-tab="appointments">
                    <i class="fas fa-user-injured"></i>
                    <span>Patients</span>
                </a>
            </li>
            <li>
                <a href="#sample-collections" class="mobile-nav-item nav-link" data-tab="sample-collections">
                    <i class="fas fa-vial"></i>
                    <span>Collections</span>
                </a>
            </li>
            <li>
                <a href="#agents" class="mobile-nav-item nav-link" data-tab="agents">
                    <i class="fas fa-truck"></i>
                    <span>Agents</span>
                </a>
            </li>
            <li>
                <a href="#reports" class="mobile-nav-item nav-link" data-tab="reports">
                    <i class="fas fa-file-medical"></i>
                    <span>Reports</span>
                </a>
            </li>
            <li>
                <a href="#verification-status" class="mobile-nav-item nav-link" data-tab="verification-status">
                    <i class="fas fa-check-circle"></i>
                    <span>Verification</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="tab-content">
            <div class="tab-pane active" id="dashboard">
                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">Total Appointments</div>
                            <div class="card-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                        </div>
                        <div class="card-value" id="totalAppointments">0</div>
                        <div class="card-description">Active patient bookings</div>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">Scheduled Pickups</div>
                            <div class="card-icon">
                                <i class="fas fa-vial"></i>
                            </div>
                        </div>
                        <div class="card-value" id="scheduledPickups">0</div>
                        <div class="card-description">Sample collections today</div>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">Pending Reports</div>
                            <div class="card-icon">
                                <i class="fas fa-file-medical"></i>
                            </div>
                        </div>
                        <div class="card-value" id="pendingReports">0</div>
                        <div class="card-description">Reports awaiting review</div>
                    </div>
                </div>
            </div>

            <div class="tab-pane" id="appointments">
                <div class="card">
                    <div class="card-header">
                        <div class="header-content">
                            <div class="header-title">
                                <h3 class="card-title">
                                    <i class="fas fa-user-injured"></i>
                                    Patient Records
                                </h3>
                                <p class="card-subtitle">Manage patient bookings and appointments</p>
                            </div>
                            <div class="header-actions">
                                <button class="btn btn-primary" onclick="refreshPatientRecords()">
                                    <i class="fas fa-sync-alt"></i> Refresh
                                </button>
                                <button class="btn btn-success" onclick="exportPatientRecords()">
                                    <i class="fas fa-download"></i> Export
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="search-filter-section">
                        <div class="search-container">
                            <div class="search-input-wrapper">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="appointmentSearchInput" class="form-control search-input" placeholder="Search by booking ID, barcode, patient name, or test...">
                                <button class="clear-search" onclick="clearSearch('appointmentSearchInput')" style="display: none;">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <div class="filter-container">
                            <div class="filter-group">
                                <label class="filter-label">Status</label>
                                <select id="appointmentStatusFilter" class="form-control filter-select">
                                    <option value="">All Status</option>
                                    <option value="assigned">Agent Assigned</option>
                                    <option value="unassigned">Unassigned</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">Payment Status</label>
                                <select id="appointmentPaymentFilter" class="form-control filter-select">
                                    <option value="">All Payments</option>
                                    <option value="paid">Paid</option>
                                    <option value="pending">Pending</option>
                                    <option value="failed">Failed</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">From Date</label>
                                <input type="date" id="appointmentDateFromFilter" class="form-control filter-date" placeholder="From Date">
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">To Date</label>
                                <input type="date" id="appointmentDateToFilter" class="form-control filter-date" placeholder="To Date">
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">&nbsp;</label>
                                <button class="btn btn-secondary filter-clear" onclick="clearAllFilters('appointments')">
                                    <i class="fas fa-eraser"></i> Clear Filters
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table>
                            <thead>
                                <tr>
                                    <th>Booking ID</th>
                                    <th>Patient</th>
                                    <th>Test</th>
                                    <th>Barcode</th>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Payment</th>
                                    <th>Agent Assigned</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="appointmentsTable"></tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="tab-pane" id="sample-collections">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Sample Collections</h3>
                    </div>
                    <div class="search-filter-section">
                        <div class="search-container">
                            <div class="search-input-wrapper">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="collectionSearchInput" class="form-control search-input" placeholder="Search by patient name, test, or barcode...">
                                <button class="clear-search" onclick="clearSearch('collectionSearchInput')" style="display: none;">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <div class="filter-container">
                            <div class="filter-group">
                                <label class="filter-label">Collection Status</label>
                                <select id="collectionStatusFilter" class="form-control filter-select">
                                    <option value="">All Status</option>
                                    <option value="assigned">Assigned</option>
                                    <option value="unassigned">Not Assigned</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">Assigned Agent</label>
                                <select id="collectionAgentFilter" class="form-control filter-select">
                                    <option value="">All Agents</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">Collection Date</label>
                                <input type="date" id="collectionDateFilter" class="form-control filter-date">
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">&nbsp;</label>
                                <button class="btn btn-secondary filter-clear" onclick="clearAllFilters('collections')">
                                    <i class="fas fa-eraser"></i> Clear Filters
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table>
                            <thead>
                                <tr>
                                    <th>Patient</th>
                                    <th>Test</th>
                                    <th>Barcode</th>
                                    <th>Collection Date</th>
                                    <th>Status</th>
                                    <th>Agent</th>
                                </tr>
                            </thead>
                            <tbody id="collectionsTable"></tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="tab-pane" id="agents">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Pickup Agents</h3>
                    </div>
                    <div class="search-filter-section">
                        <div class="search-container">
                            <div class="search-input-wrapper">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="agentSearchInput" class="form-control search-input" placeholder="Search by agent name, phone, or status...">
                                <button class="clear-search" onclick="clearSearch('agentSearchInput')" style="display: none;">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <div class="filter-container">
                            <div class="filter-group">
                                <label class="filter-label">Agent Status</label>
                                <select id="agentStatusFilter" class="form-control filter-select">
                                    <option value="">All Status</option>
                                    <option value="available">Available</option>
                                    <option value="busy">Busy</option>
                                    <option value="offline">Offline</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">&nbsp;</label>
                                <button class="btn btn-secondary filter-clear" onclick="clearAllFilters('agents')">
                                    <i class="fas fa-eraser"></i> Clear Filters
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table>
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Phone</th>
                                    <th>Status</th>
                                    <th>Current Load</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="agentsTable"></tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="tab-pane" id="reports">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Reports</h3>
                        <div class="header-actions">
                            <button class="btn btn-primary" onclick="openModal('uploadReportModal')">
                                <i class="fas fa-upload"></i> Upload Report
                            </button>
                        </div>
                    </div>
                    <div class="search-filter-section">
                        <div class="search-container">
                            <div class="search-input-wrapper">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="reportSearchInput" class="form-control search-input" placeholder="Search by patient name, booking ID, or test...">
                                <button class="clear-search" onclick="clearSearch('reportSearchInput')" style="display: none;">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <div class="filter-container">
                            <div class="filter-group">
                                <label class="filter-label">Assignment Status</label>
                                <select id="reportStatusFilter" class="form-control filter-select">
                                    <option value="">All Status</option>
                                    <option value="Not Assigned">Not Assigned</option>
                                    <option value="Assigned">Assigned</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">Sample Status</label>
                                <select id="sampleStatusFilter" class="form-control filter-select">
                                    <option value="">All Sample Status</option>
                                    <option value="Pending">Pending</option>
                                    <option value="Collected">Collected</option>
                                    <option value="Delivered">Delivered</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">&nbsp;</label>
                                <button class="btn btn-secondary filter-clear" onclick="clearAllFilters('reports')">
                                    <i class="fas fa-eraser"></i> Clear Filters
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table>
                            <thead>
                                <tr>
                                    <th>Booking ID</th>
                                    <th>Barcode</th>
                                    <th>Patient</th>
                                    <th>Test</th>
                                    <th>Sample Status</th>
                                    <th>Report</th>
                                    <th>Assign Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="reportsTable"></tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="tab-pane" id="verification-status">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Report Verification Status</h3>
                    </div>
                    <div class="search-filter-section">
                        <div class="search-container">
                            <div class="search-input-wrapper">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="verificationSearchInput" class="form-control search-input" placeholder="Search by patient name, booking ID, or test...">
                                <button class="clear-search" onclick="clearSearch('verificationSearchInput')" style="display: none;">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <div class="filter-container">
                            <div class="filter-group">
                                <label class="filter-label">Verification Status</label>
                                <select id="verificationStatusFilter" class="form-control filter-select">
                                    <option value="">All Status</option>
                                    <option value="pending">Pending</option>
                                    <option value="verified">Verified</option>
                                    <option value="sent">Sent</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">Verification Date</label>
                                <input type="date" id="verificationDateFilter" class="form-control filter-date">
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">&nbsp;</label>
                                <button class="btn btn-secondary filter-clear" onclick="clearAllFilters('verification')">
                                    <i class="fas fa-eraser"></i> Clear Filters
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table>
                            <thead>
                                <tr>
                                    <th>Booking ID</th>
                                    <th>Patient</th>
                                    <th>Test</th>
                                    <th>Barcode</th>
                                    <th>Doctor Verification</th>
                                    <th>Report</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody id="verificationStatusTable"></tbody>
                        </table>
                    </div>
                </div>
            </div>

 <!-- ...existing code... -->
<div class="modal" id="uploadReportModal">
    <div class="modal-content">
        <span class="close" onclick="closeModal('uploadReportModal')">&times;</span>
        <h2>Upload Report</h2>
        <form id="uploadReportForm" action="/staff/api/receptionist/upload-report" method="POST" enctype="multipart/form-data">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
            <div class="form-group">
                <label for="bookingSearch">Search Bookings</label>
                <input type="text" id="bookingSearch" class="form-control" placeholder="Search by patient, test, or date..." autocomplete="off" oninput="filterBookingsDropdown()">
                <!-- Hidden field to store selected booking ID -->
                <input type="hidden" id="uploadBookingId" name="booking_id" required>
                <!-- Results will show here -->
                <div id="bookingSearchResults" style="position:relative;z-index:10;background:#fff;border:1px solid #eee;max-height:200px;overflow-y:auto;"></div>
            </div>
            <div class="form-group">
                <label for="report_file">Report File (PDF)</label>
                <input type="file" id="report_file" name="report_file" class="form-control" accept="application/pdf" required>
            </div>
            <div class="form-buttons">
                <button type="submit" class="btn btn-primary">Upload</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal('uploadReportModal')">Cancel</button>
            </div>
        </form>
    </div>
</div>
<!-- ...existing code... -->

            <!-- View Report Modal -->
            <div class="modal" id="viewReportModal">
                <div class="modal-content modal-lg">
                    <span class="close" onclick="closeModal('viewReportModal')">&times;</span>
                    <h2>View Report</h2>
                    <div class="report-viewer">
                        <iframe id="reportViewer" style="width: 100%; height: 80vh; border: none;"></iframe>
                    </div>
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('viewReportModal')">Close</button>
                    </div>
                </div>
            </div>

            <!-- Assign Report Modal -->
            <div class="modal" id="assignReportModal">
                <div class="modal-content">
                    <span class="close" onclick="closeModal('assignReportModal')">&times;</span>
                    <h2>Assign Report to Doctor</h2>
                    <form id="assignReportForm">
                        <input type="hidden" id="assignReportId" name="report_id">
                        <div class="form-group">
                            <label for="doctorSelect" class="required">Select Doctor</label>
                            <select class="form-control" id="doctorSelect" name="doctor_id" required>
                                <option value="">Select a doctor...</option>
                            </select>
                        </div>
                        <div class="form-buttons">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('assignReportModal')">Close</button>
                            <button type="submit" class="btn btn-primary">Assign Report</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

       

        <!-- View Patient Modal -->
        <div class="modal" id="viewPatientModal">
            <div class="modal-content">
                <span class="close" onclick="closeModal('viewPatientModal')">×</span>
                <h2>Patient Details</h2>
                <div id="viewPatientDetails">
                    <p><strong>ID:</strong> <span id="viewPatientId"></span></p>
                    <p><strong>Name:</strong> <span id="viewPatientName"></span></p>
                    <p><strong>Contact:</strong> <span id="viewPatientContact"></span></p>
                    <p><strong>Email:</strong> <span id="viewPatientEmail"></span></p>
                    <p><strong>Last Visit:</strong> <span id="viewPatientLastVisit"></span></p>
                </div>
                <div class="form-buttons">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('viewPatientModal')">Close</button>
                </div>
            </div>
        </div>

        <!-- Edit Patient Modal -->
        <div class="modal" id="editPatientModal">
            <div class="modal-content">
                <span class="close" onclick="closeModal('editPatientModal')">×</span>
                <h2>Edit Patient</h2>
                <form id="editPatientForm" action="/staff/api/patients/update" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                    <input type="hidden" id="editPatientId" name="patient_id">
                    <div class="form-group">
                        <label for="editFullName" class="required">Full Name</label>
                        <input type="text" class="form-control" id="editFullName" name="fullName" required>
                    </div>
                    <div class="form-group">
                        <label for="editEmail" class="required">Email</label>
                        <input type="email" class="form-control" id="editEmail" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="editPhone" class="required">Phone</label>
                        <input type="text" class="form-control" id="editPhone" name="phone" required>
                    </div>
                    <div class="form-group">
                        <label for="editAddress" class="required">Address</label>
                        <textarea class="form-control" id="editAddress" name="address" required></textarea>
                    </div>
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('editPatientModal')">Close</button>
                        <button type="submit" class="btn btn-primary">Update Patient</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Add Pickup Modal -->
        <div class="modal" id="addPickupModal">
            <div class="modal-content">
                <span class="close" onclick="closeModal('addPickupModal')">&times;</span>
                <h2>Schedule New Pickup</h2>
                <form id="addPickupForm" action="/staff/api/pickups/add" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                    <div class="form-group">
                        <label for="pickup_booking_id" class="required">Booking ID</label>
                        <input type="number" class="form-control" id="pickup_booking_id" name="booking_id" required>
                    </div>
                    <div class="form-group">
                        <label for="collection_date" class="required">Collection Date</label>
                        <input type="date" class="form-control" id="collection_date" name="collection_date" required>
                    </div>
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('addPickupModal')">Close</button>
                        <button type="submit" class="btn btn-primary">Schedule Pickup</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Add Report Modal -->
        <div class="modal" id="addReportModal">
            <div class="modal-content">
                <span class="close" onclick="closeModal('addReportModal')">&times;</span>
                <h2>Generate New Report</h2>
                <form id="addReportForm" action="/staff/api/reports/add" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                    <div class="form-group">
                        <label for="report_booking_id" class="required">Booking ID</label>
                        <input type="number" class="form-control" id="report_booking_id" name="booking_id" required>
                    </div>
                    <div class="form-group">
                        <label for="report_type" class="required">Report Type</label>
                        <select class="form-control" id="report_type" name="report_type" required>
                            <option value="standard">Standard</option>
                            <option value="detailed">Detailed</option>
                            <option value="summary">Summary</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="report_format" class="required">Report Format</label>
                        <select class="form-control" id="report_format" name="report_format" required>
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="report_file" class="required">Report File</label>
                        <input type="file" class="form-control" id="report_file" name="report_file" required>
                    </div>
                    <div class="form-group">
                        <label for="doctor_id">Doctor</label>
                        <select class="form-control" id="doctor_id" name="doctor_id">
                            <option value="">Select Doctor</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="doctor_review">Doctor Review</label>
                        <textarea class="form-control" id="doctor_review" name="doctor_review"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="comments">Comments</label>
                        <textarea class="form-control" id="comments" name="comments"></textarea>
                    </div>
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('addReportModal')">Close</button>
                        <button type="submit" class="btn btn-primary">Generate Report</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Schedule Collection Modal -->
        <div class="modal" id="scheduleCollectionModal">
            <div class="modal-content">
                <span class="close" onclick="closeModal('scheduleCollectionModal')">&times;</span>
                <h2>Schedule Sample Collection</h2>
                <div class="patient-details mb-3">
                    <h4>Patient Details</h4>
                    <div class="row">
                        <div class="col">
                            <p><strong>Name:</strong> <span id="schedulePatientName"></span></p>
                            <p><strong>Test:</strong> <span id="scheduleTestName"></span></p>
                        </div>
                        <div class="col">
                            <p><strong>Booking ID:</strong> <span id="scheduleBookingId"></span></p>
                            <p><strong>Barcode:</strong> <span id="scheduleBarcode"></span></p>
                        </div>
                    </div>
                </div>
                <form id="scheduleCollectionForm">
                    <input type="hidden" id="scheduleBookingIdInput" name="booking_id">
                    <div class="form-group">
                        <label for="scheduleAgentSelect" class="required">Select Agent</label>
                        <select class="form-control" id="scheduleAgentSelect" name="agent_id" required>
                            <option value="">Select an agent...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="scheduleDate" class="required">Collection Date</label>
                        <input type="date" class="form-control" id="scheduleDate" name="collection_date" required>
                    </div>
                    <div class="form-group">
                        <label for="scheduleTime" class="required">Collection Time</label>
                        <input type="time" class="form-control" id="scheduleTime" name="collection_time" required>
                    </div>
                    <div class="form-group">
                        <label for="scheduleNotes">Notes</label>
                        <textarea class="form-control" id="scheduleNotes" name="notes" rows="3"></textarea>
                    </div>
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('scheduleCollectionModal')">Close</button>
                        <button type="submit" class="btn btn-primary">Schedule Collection</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Assign Agent Modal -->
        <div id="assignAgentModal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="closeModal('assignAgentModal')">&times;</span>
                <h2>Assign Agent</h2>
                <div class="booking-details">
                    <p><strong>Patient Name:</strong> <span id="modalPatientName"></span></p>
                    <p><strong>Test Name:</strong> <span id="modalTestName"></span></p>
                    <p><strong>Booking ID:</strong> <span id="modalBookingId"></span></p>
                    <p><strong>Barcode:</strong> <span id="modalBarcode"></span></p>
                </div>
                <form id="assignAgentForm">
                    <input type="hidden" id="assignBookingId" name="booking_id">
                    <div class="form-group">
                        <label for="agentSelect">Select Agent:</label>
                        <select id="agentSelect" name="agent_id" required>
                            <option value="">Select an agent...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="collectionDate">Collection Date:</label>
                        <input type="date" id="collectionDate" name="collection_date" required>
                    </div>
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('assignAgentModal')">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="assignAgentBtn">Assign Agent</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- View Report Modal -->
        <div class="modal" id="viewReportModal">
            <div class="modal-content">
                <span class="close" onclick="closeModal('viewReportModal')">&times;</span>
                <h2>Report Details</h2>
                <div id="reportDetails"></div>
                <div class="form-buttons">
                    <button class="btn btn-secondary" onclick="closeModal('viewReportModal')">Close</button>
                    <button class="btn btn-primary" onclick="downloadReport(currentReportId)">Download</button>
                    <button class="btn btn-danger" onclick="deleteReport(currentReportId)">Delete</button>
                </div>
            </div>
        </div>

        <!-- Export Reports Modal -->
        <div class="modal" id="exportReportsModal">
            <div class="modal-content">
                <span class="close" onclick="closeModal('exportReportsModal')">&times;</span>
                <h2>Export Reports</h2>
                <form id="exportReportsForm" onsubmit="handleExportReports(event)">
                    <div class="form-group">
                        <label for="exportStartDate" class="required">Start Date</label>
                        <input type="date" class="form-control" id="exportStartDate" required>
                    </div>
                    <div class="form-group">
                        <label for="exportEndDate" class="required">End Date</label>
                        <input type="date" class="form-control" id="exportEndDate" required>
                    </div>
                    <div class="form-group">
                        <label for="exportType">Report Type</label>
                        <select class="form-control" id="exportType">
                            <option value="all">All Types</option>
                            <option value="standard">Standard</option>
                            <option value="detailed">Detailed</option>
                            <option value="summary">Summary</option>
                        </select>
                    </div>
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('exportReportsModal')">Close</button>
                        <button type="submit" class="btn btn-primary">Export</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- View Collection Details Modal -->
        <div class="modal" id="viewCollectionDetailsModal">
            <div class="modal-content">
                <span class="close" onclick="closeModal('viewCollectionDetailsModal')">&times;</span>
                <div class="modal-header">
                    <h2><i class="fas fa-clipboard-list"></i> Collection Details</h2>
                    <p class="modal-subtitle">Complete information about the sample collection</p>
                </div>
                <div class="collection-details">
                    <div class="detail-section">
                        <h4><i class="fas fa-user"></i> Patient Information</h4>
                        <p><strong>Name:</strong> <span id="viewPatientName"></span></p>
                        <p><strong>Test:</strong> <span id="viewTestName"></span></p>
                        <p><strong>Amount:</strong> <span>₹<span id="viewTestAmount"></span></span></p>
                        <p><strong>Address:</strong> <span id="viewAddress"></span></p>
                    </div>
                    <div class="detail-section">
                        <h4><i class="fas fa-calendar-alt"></i> Booking Information</h4>
                        <p><strong>Booking ID:</strong> <span id="viewBookingId"></span></p>
                        <p><strong>Barcode:</strong> <span id="viewBarcode"></span></p>
                        <p><strong>Date:</strong> <span id="viewBookingDate"></span></p>
                        <p><strong>Time:</strong> <span id="viewAppointmentTime"></span></p>
                        <p><strong>Status:</strong> <span id="viewBookingStatus" class="status-badge"></span></p>
                    </div>
                    <div class="detail-section">
                        <h4><i class="fas fa-vial"></i> Collection Information</h4>
                        <p><strong>Agent:</strong> <span id="viewAgentName"></span></p>
                        <p><strong>Collection Status:</strong> <span id="viewCollectionStatus" class="status-badge"></span></p>
                    </div>
                    <div class="detail-section">
                        <h4><i class="fas fa-credit-card"></i> Payment Information</h4>
                        <p><strong>Status:</strong> <span id="viewPaymentStatus" class="status-badge"></span></p>
                        <p><strong>Amount:</strong> <span>₹<span id="viewPaymentAmount"></span></span></p>
                    </div>
                </div>
                <div class="form-buttons">
                    <button type="button" class="btn btn-info" onclick="generatePatientBill()">
                        <i class="fas fa-file-invoice"></i> Generate Bill
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="printCollectionDetails()">
                        <i class="fas fa-print"></i> Print Details
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('viewCollectionDetailsModal')">
                        <i class="fas fa-times"></i> Close
                    </button>
                </div>
            </div>
        </div>

        <style>
            .collection-details {
                padding: 1rem;
            }
            .detail-section {
                margin-bottom: 1.5rem;
                padding: 1rem;
                background-color: #f8f9fa;
                border-radius: 4px;
            }
            .detail-section h4 {
                color: var(--dark-blue);
                margin-bottom: 1rem;
                padding-bottom: 0.5rem;
                border-bottom: 1px solid #dee2e6;
            }
            .detail-section p {
                margin: 0.5rem 0;
            }
            .detail-section strong {
                color: var(--dark-blue);
                min-width: 150px;
                display: inline-block;
            }
                    /* --- Improved Assign Agent Modal Styles --- */
        #assignAgentModal .modal-content {
            background: #fff;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 24px rgba(0,47,108,0.12);
            max-width: 500px;
            min-height: 550px;
            max-height: 90vh;
            margin: 3% auto;
            position: relative;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        #assignAgentModal h2 {
            color: var(--dark-blue);
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            border-bottom: 2px solid var(--light-blue);
            padding-bottom: 0.5rem;
        }

        #assignAgentModal .booking-details {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 1rem 1.2rem;
            margin-bottom: 1.5rem;
            font-size: 1rem;
        }

        #assignAgentModal .booking-details p {
            margin: 0.4rem 0;
            color: #333;
        }

        #assignAgentModal .form-group {
            margin-bottom: 1.2rem;
        }

        #assignAgentModal .form-group label {
            display: block;
            color: var(--dark-blue);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        #assignAgentModal .form-group select,
        #assignAgentModal .form-group input[type="date"] {
            width: 100%;
            padding: 0.7rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            box-sizing: border-box;
        }

        #assignAgentModal .form-group select:focus,
        #assignAgentModal .form-group input[type="date"]:focus {
            outline: none;
            border-color: var(--dark-blue);
            box-shadow: 0 0 0 2px rgba(0,47,108,0.1);
        }

        #assignAgentModal .btn-primary {
            background: var(--gradient-primary) !important;
            color: #fff !important;
            font-weight: 600;
            padding: 0.8rem 1.5rem !important;
            border-radius: 6px;
            border: none !important;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-block !important;
            text-decoration: none;
            min-width: 120px;
            text-align: center;
        }

        #assignAgentModal .btn-primary:hover {
            background: var(--gradient-secondary) !important;
            color: #fff !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(245, 130, 32, 0.3);
        }

        #assignAgentModal form {
            display: flex;
            flex-direction: column;
            flex: 1;
        }

        #assignAgentModal .form-buttons {
            display: flex !important;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: auto;
            padding-top: 1.5rem;
            border-top: 1px solid #eee;
            flex-shrink: 0;
            background: white;
            position: relative;
            z-index: 10;
        }

        #assignAgentModal .btn-secondary {
            background: #6c757d !important;
            color: #fff !important;
            font-weight: 600;
            padding: 0.8rem 1.5rem !important;
            border-radius: 6px;
            border: none !important;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-block !important;
            text-decoration: none;
            min-width: 120px;
            text-align: center;
        }

        #assignAgentModal .btn-secondary:hover {
            background: #5a6268 !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        /* Debug: Ensure buttons are always visible */
        #assignAgentModal button {
            visibility: visible !important;
            opacity: 1 !important;
            display: inline-block !important;
            position: relative !important;
            z-index: 999 !important;
        }

        /* Ensure the modal has proper height and scrolling */
        #assignAgentModal {
            overflow-y: auto !important;
        }

        #assignAgentModal .modal-content {
            position: relative !important;
            margin: 2% auto !important;
        }

        /* Ensure buttons are visible on mobile */
        @media (max-width: 768px) {
            #assignAgentModal .modal-content {
                width: 95%;
                margin: 2% auto;
                padding: 1.5rem;
                min-height: auto;
                max-height: 90vh;
                overflow-y: auto;
            }

            #assignAgentModal .form-buttons {
                flex-direction: column;
                gap: 0.8rem;
                margin-top: 1.5rem;
                position: sticky;
                bottom: 0;
                background: white;
                padding: 1rem 0;
            }

            #assignAgentModal .btn-primary,
            #assignAgentModal .btn-secondary {
                width: 100%;
                padding: 1rem;
                font-size: 1rem;
            }
        }
        </style>

        <!-- View Agent Details Modal -->
        <div class="modal" id="viewAgentDetailsModal">
            <div class="modal-content">
                <span class="close" onclick="closeModal('viewAgentDetailsModal')">&times;</span>
                <h2>Agent Details</h2>
                <div class="agent-details">
                    <div class="detail-section">
                        <h4>Personal Information</h4>
                        <p><strong>Name:</strong> <span id="viewAgentName"></span></p>
                        <p><strong>Phone:</strong> <span id="viewAgentPhone"></span></p>
                        <p><strong>Email:</strong> <span id="viewAgentEmail"></span></p>
                        <p><strong>Status:</strong> <span id="viewAgentStatus" class="status-badge"></span></p>
                    </div>
                    <div class="detail-section">
                        <h4>Performance Metrics</h4>
                        <p><strong>Current Load:</strong> <span id="viewAgentLoad"></span></p>
                        <p><strong>Total Collections:</strong> <span id="viewAgentTotalCollections"></span></p>
                        <p><strong>Success Rate:</strong> <span id="viewAgentSuccessRate"></span></p>
                    </div>
                    <div class="detail-section">
                        <h4>Today's Schedule</h4>
                        <div id="viewAgentSchedule"></div>
                    </div>
                </div>
                <div class="form-buttons">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('viewAgentDetailsModal')">Close</button>
                </div>
            </div>
        </div>

        <!-- Update Agent Status Modal -->
        <div class="modal" id="updateAgentStatusModal">
            <div class="modal-content">
                <span class="close" onclick="closeModal('updateAgentStatusModal')">&times;</span>
                <h2>Update Agent Status</h2>
                <form id="updateAgentStatusForm">
                    <input type="hidden" id="updateAgentId" name="agent_id">
                    <div class="form-group">
                        <label for="agentStatus" class="required">Status</label>
                        <select class="form-control" id="agentStatus" name="status" required>
                            <option value="available">Available</option>
                            <option value="busy">Busy</option>
                            <option value="offline">Offline</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="statusNotes">Notes</label>
                        <textarea class="form-control" id="statusNotes" name="notes" rows="3"></textarea>
                    </div>
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('updateAgentStatusModal')">Close</button>
                        <button type="submit" class="btn btn-primary">Update Status</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Global CSRF token for JS AJAX requests -->
    <input type="hidden" id="csrf_token" value="{{ csrf_token }}">

    <script>
        console.log = function() {};

        // Utility function to safely get elements
        function safeGetElement(id) {
            const element = document.getElementById(id);
            if (!element) {
                console.warn(`Element with id '${id}' not found`);
            }
            return element;
        }

        function showLoader() {
            document.getElementById('loaderOverlay').style.display = 'flex';
        }
        function hideLoader() {
            document.getElementById('loaderOverlay').style.display = 'none';
        }


        
        // Tab navigation for both desktop and mobile
        const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-item');
        const tabPanes = document.querySelectorAll('.tab-pane');

        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const tabId = link.dataset.tab;

                // Add haptic feedback for mobile devices
                if ('vibrate' in navigator && link.classList.contains('mobile-nav-item')) {
                    navigator.vibrate(50);
                }

                // Remove active class from all navigation items
                navLinks.forEach(l => l.classList.remove('active'));

                // Add active class to clicked item
                link.classList.add('active');

                // Update corresponding navigation item (desktop/mobile)
                const correspondingLink = document.querySelector(
                    link.classList.contains('nav-link')
                        ? `.mobile-nav-item[data-tab="${tabId}"]`
                        : `.nav-link[data-tab="${tabId}"]`
                );
                if (correspondingLink) {
                    correspondingLink.classList.add('active');
                }

                // Switch tab panes
                tabPanes.forEach(pane => pane.classList.remove('active'));
                const targetPane = document.getElementById(tabId);
                if (targetPane) {
                    targetPane.classList.add('active');
                }

                // Load tab data
                loadTabData(tabId);
            });

            // Add touch event listeners for better mobile experience
            if (link.classList.contains('mobile-nav-item')) {
                link.addEventListener('touchstart', (e) => {
                    link.style.transform = 'translateY(-2px) scale(0.98)';
                }, { passive: true });

                link.addEventListener('touchend', (e) => {
                    setTimeout(() => {
                        if (!link.classList.contains('active')) {
                            link.style.transform = '';
                        }
                    }, 150);
                }, { passive: true });
            }
        });

        // Load tab data
        async function loadTabData(tab) {
            try {
                switch(tab) {
                    case 'dashboard':
                        await loadDashboardData();
                        break;
                    case 'appointments':
                        await loadAppointments();
                        break;
                    case 'sample-collections':
                        await loadSampleCollections();
                        break;
                    case 'agents':
                        await loadPickupAgents();
                        break;
                    case 'reports':
                        await loadReports();
                        break;
                    case 'verification-status':
                        await loadVerificationStatus();
                        break;
                }
            } catch (error) {
                console.error(`Error loading ${tab} data:`, error);
                showAlert(`Error loading ${tab} data`, 'danger');
            }
        }

        // Load Dashboard Data
        async function loadDashboardData() {
            try {
                const response = await fetch('/staff/api/receptionist/dashboard');
                if (!response.ok) throw new Error('Failed to fetch dashboard data');
                const data = await response.json();
                
                // Animate the numbers counting up
                animateValue('totalAppointments', 0, data.total_appointments, 1000);
                animateValue('scheduledPickups', 0, data.scheduled_pickups, 1000);
                animateValue('pendingReports', 0, data.pending_reports, 1000);
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                showAlert('Error loading dashboard data', 'danger');
            }
        }

        // Add animation function for counting up numbers
        function animateValue(elementId, start, end, duration) {
            const element = document.getElementById(elementId);
            const range = end - start;
            const increment = range / (duration / 16); // 60fps
            let current = start;
            
            const animate = () => {
                current += increment;
                if ((increment > 0 && current >= end) || (increment < 0 && current <= end)) {
                    element.textContent = end;
                } else {
                    element.textContent = Math.round(current);
                    requestAnimationFrame(animate);
                }
            };
            
            requestAnimationFrame(animate);
        }

        // Load Appointments
        async function loadAppointments() {
            try {
                console.log('Fetching appointments...');

                // Get search and filter values
                const searchInput = document.getElementById('appointmentSearchInput')?.value || '';
                const statusFilter = document.getElementById('appointmentStatusFilter')?.value || '';
                const paymentFilter = document.getElementById('appointmentPaymentFilter')?.value || '';
                const dateFromFilter = document.getElementById('appointmentDateFromFilter')?.value || '';
                const dateToFilter = document.getElementById('appointmentDateToFilter')?.value || '';

                // Build query parameters
                const params = new URLSearchParams();
                if (searchInput) params.append('search', searchInput);
                if (statusFilter) params.append('status', statusFilter);
                if (paymentFilter) params.append('payment_status', paymentFilter);
                if (dateFromFilter) params.append('date_from', dateFromFilter);
                if (dateToFilter) params.append('date_to', dateToFilter);

                // Make the API call with query parameters
                const response = await fetch(`/staff/api/receptionist/appointments?${params.toString()}`);
                if (!response.ok) throw new Error('Failed to fetch appointments');
                const appointments = await response.json();
                console.log('Received appointments:', appointments);
                
                const tbody = document.getElementById('appointmentsTable');
                if (!tbody) {
                    console.error('Could not find appointmentsTable element');
                    return;
                }
                
                if (!appointments || appointments.length === 0) {
                    console.log('No appointments found in the response');
                    tbody.innerHTML = '<tr><td colspan="9">No appointments found</td></tr>';
                    return;
                }
                
                // Log each appointment's agent status
                appointments.forEach(a => {
                    console.log(`Appointment ${a.id}: agent_name = ${a.agent_name}`);
                });

                tbody.innerHTML = appointments.map(a => {
                    const hasAgent = a.agent_name && a.agent_name.trim() !== '';
                    console.log(`Rendering appointment ${a.id}, hasAgent: ${hasAgent}`);
                    
                    return `
                    <tr>
                        <td>${a.id}</td>
                        <td>${a.patient_name || 'N/A'}</td>
                        <td>${a.test_name}</td>
                        <td>${a.barcode || 'N/A'}</td>
                        <td>${a.booking_date}</td>
                        <td>₹${a.test_amount || 0}</td>
                        <td><span class="status-badge ${getBadgeClass(a.payment_status)}">${a.payment_status || 'Pending'}</span></td>
                        <td>${a.agent_name ? a.agent_name : 'Not Assigned'}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-success" onclick="generatePatientBillDirect(${a.id})" title="Generate Professional Bill">
                                    <i class="fas fa-file-invoice"></i>
                                </button>
                                ${!hasAgent ?
                                    `<button class="btn btn-sm btn-primary" onclick="assignAgent(${a.id})">Assign Agent</button>` :
                                    `<button class="btn btn-sm btn-info" onclick="viewCollectionDetails(${a.id})">View Details</button>`
                                }
                            </div>
                        </td>
                    </tr>`;
                }).join('');
                console.log('Appointments table updated');
            } catch (error) {
                console.error('Error loading appointments:', error);
                showAlert('Error loading appointments', 'danger');
            }
        }

        // Load Sample Collections
        async function loadSampleCollections() {
            try {
                const searchInput = document.getElementById('collectionSearchInput').value;
                const statusFilter = document.getElementById('collectionStatusFilter').value;
                const agentFilter = document.getElementById('collectionAgentFilter').value;
                const dateFilter = document.getElementById('collectionDateFilter').value;

                // Build query parameters
                const params = new URLSearchParams();
                if (searchInput) params.append('search', searchInput);
                if (statusFilter) params.append('status', statusFilter);
                if (agentFilter) params.append('agent_id', agentFilter);
                if (dateFilter) params.append('date', dateFilter);

                const response = await fetch(`/staff/api/receptionist/sample-collections?${params.toString()}`);
                if (!response.ok) throw new Error('Failed to fetch sample collections');
                const collections = await response.json();
                
                const tbody = document.getElementById('collectionsTable');
                tbody.innerHTML = collections.length ? collections.map(c => `
                    <tr>
                        <td>${c.patient_name}</td>
                        <td>${c.test_name}</td>
                        <td>${c.barcode || 'N/A'}</td>
                        <td>${c.collection_date}</td>
                        <td><span class="status-badge ${getBadgeClass(c.collection_status)}">${c.collection_status}</span></td>
                        <td>${c.agent_name || 'Not Assigned'}</td>
                    </tr>
                `).join('') : '<tr><td colspan="6">No sample collections found</td></tr>';
            } catch (error) {
                console.error('Error loading sample collections:', error);
                showAlert('Error loading sample collections', 'danger');
            }
        }

        // Load Pickup Agents
        async function loadPickupAgents() {
            try {
                const response = await fetch('/staff/api/receptionist/pickup-agents');
                if (!response.ok) throw new Error('Failed to fetch pickup agents');
                const agents = await response.json();
                
                const tbody = document.getElementById('agentsTable');
                tbody.innerHTML = agents.length ? agents.map(a => `
                    <tr>
                        <td>${a.name}</td>
                        <td>${a.phone}</td>
                        <td><span class="status-badge ${getBadgeClass(a.status)}">${a.status}</span></td>
                        <td>${a.current_load || 0}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-info" onclick="viewAgentDetails(${a.id})">View Details</button>
                                <button class="btn btn-sm btn-warning" onclick="updateAgentStatus(${a.id})">Update Status</button>
                            </div>
                        </td>
                    </tr>
                `).join('') : '<tr><td colspan="5">No pickup agents found</td></tr>';
            } catch (error) {
                console.error('Error loading pickup agents:', error);
                showAlert('Error loading pickup agents', 'danger');
            }
        }

        // Load Reports
        async function loadReports() {
            try {
                const searchInput = document.getElementById('reportSearchInput').value;
                const statusFilter = document.getElementById('reportStatusFilter').value;
                const sampleStatusFilter = document.getElementById('sampleStatusFilter').value;

                const response = await fetch('/staff/api/reports');
                if (!response.ok) throw new Error('Failed to fetch reports');
                let reports = await response.json();

                // Apply filters with improved search logic
                reports = reports.filter(report => {
                    // Convert search input and report fields to lowercase for case-insensitive comparison
                    const searchLower = searchInput.toLowerCase().trim();
                    
                    // Check if any of these fields match the search input
                    const matchesSearch = !searchInput || 
                        (report.patient_name && report.patient_name.toLowerCase().includes(searchLower)) ||
                        (report.booking_id && report.booking_id.toString().includes(searchLower)) ||
                        (report.barcode && report.barcode.toLowerCase().includes(searchLower)) ||
                        (report.test_name && report.test_name.toLowerCase().includes(searchLower));

                    // Apply status filters
                    const matchesStatus = !statusFilter || 
                        (report.assign_status && report.assign_status.toLowerCase() === statusFilter.toLowerCase());
                    
                    const matchesSampleStatus = !sampleStatusFilter || 
                        (report.sample_status && report.sample_status.toLowerCase() === sampleStatusFilter.toLowerCase());
                    
                    return matchesSearch && matchesStatus && matchesSampleStatus;
                });

                const tbody = document.getElementById('reportsTable');
                tbody.innerHTML = reports.length ? reports.map(report => `
                    <tr>
                        <td>${report.booking_id}</td>
                        <td>${report.barcode || 'N/A'}</td>
                        <td>${report.patient_name}</td>
                        <td>${report.test_name}</td>
                        <td><span class="status-badge ${getBadgeClass(report.sample_status)}">${report.sample_status || 'Pending'}</span></td>
                        <td>${report.report_url ? 
                            `<a href="/staff/uploads/reports/${report.report_url}" target="_blank" class="btn btn-sm btn-info">
                                <i class="fas fa-file-pdf"></i> View
                            </a>` : 
                            'No Report'}
                        </td>
                        <td><span class="status-badge ${getBadgeClass(report.assign_status)}">${report.assign_status || 'Not Assigned'}</span></td>
                        <td>
                            <div class="action-buttons">
                                ${!report.assign_status || report.assign_status === 'Not Assigned' ? 
                                    `<button class="btn btn-sm btn-success" onclick="openAssignModal(${report.id})">
                                        <i class="fas fa-user-md"></i> Assign
                                    </button>` :
                                    (report.report_url ?
                                        '<span class="btn btn-sm btn-success">Done</span>' :
                                        '<span class="text-muted">No Report</span>'
                                    )
                                }
                            </div>
                        </td>
                    </tr>
                `).join('') : '<tr><td colspan="8">No reports found</td></tr>';
            } catch (error) {
                console.error('Error loading reports:', error);
                showAlert('Error loading reports', 'danger');
            }
        }

        async function loadVerificationStatus() {
            try {
                // Get search and filter values
                const searchInput = document.getElementById('verificationSearchInput').value;
                const statusFilter = document.getElementById('verificationStatusFilter').value;
                const dateFilter = document.getElementById('verificationDateFilter').value;

                const response = await fetch('/staff/api/reports');
                if (!response.ok) throw new Error('Failed to fetch reports');
                let reports = await response.json();

                // Apply filters
                reports = reports.filter(report => {
                    const searchLower = searchInput.toLowerCase().trim();
                    const matchesSearch = !searchInput || 
                        (report.patient_name && report.patient_name.toLowerCase().includes(searchLower)) ||
                        (report.booking_id && report.booking_id.toString().includes(searchLower)) ||
                        (report.test_name && report.test_name.toLowerCase().includes(searchLower));

                    const matchesStatus = !statusFilter || 
                        (report.report_status && report.report_status.toLowerCase() === statusFilter.toLowerCase());

                    const matchesDate = !dateFilter || 
                        (report.created_at && report.created_at.startsWith(dateFilter));

                    return matchesSearch && matchesStatus && matchesDate;
                });

                const tbody = document.getElementById('verificationStatusTable');
                tbody.innerHTML = reports.length ? reports.map(report => {
                    const isVerified = report.report_status && report.report_status.toLowerCase() === 'verified';
                    const isSent = report.is_sent === 1;
                    return `
                        <tr>
                            <td>${report.booking_id}</td>
                            <td>${report.patient_name || ''}</td>
                            <td>${report.test_name || ''}</td>
                            <td>${report.barcode || 'N/A'}</td>
                            <td>${report.report_status ? `<span class="status-badge status-${report.report_status.toLowerCase()}">${report.report_status}</span>` : `<span class="status-badge status-pending">Pending</span>`}</td>
                            <td>${report.report_url ? 
                                `<a href="/staff/uploads/reports/${report.report_url}" target="_blank" class="btn btn-sm btn-info">
                                    <i class="fas fa-file-pdf"></i> View
                                </a>` : 
                                'No Report'}
                            </td>
                            <td>
                                ${isVerified && report.report_url ? 
                                    `<button class="btn btn-sm ${isSent ? 'btn-warning' : 'btn-success'}" onclick="openSendReportToPatientModal(${report.id})">
                                        ${isSent ? 'Resend' : 'Send'}
                                    </button>` : ''}
                            </td>
                        </tr>
                    `;
                }).join('') : '<tr><td colspan="7">No reports found</td></tr>';
            } catch (error) {
                showAlert('Error loading verification status', 'danger');
            }
        }

        // Helper Functions
        function getBadgeClass(status) {
            const statusMap = {
                'pending': 'status-pending',
                'confirmed': 'status-scheduled',
                'cancelled': 'status-cancelled',
                'completed': 'status-completed',
                'failed': 'status-failed',
                'collected': 'status-completed',
                'delivered': 'status-completed',
                'available': 'status-scheduled',
                'busy': 'status-pending',
                'inactive': 'status-cancelled'
            };
            return statusMap[status?.toLowerCase()] || 'status-pending';
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} active`;
            alert.innerHTML = `
                <span>${message}</span>
                <span class="close" onclick="this.parentElement.remove()">&times;</span>
            `;
            document.body.appendChild(alert);
            setTimeout(() => alert.remove(), 5000);
        }

        // Action Functions
        async function assignAgent(bookingId) {
            try {
                // Fetch booking details
                const bookingResponse = await fetch(`/staff/api/receptionist/booking/${bookingId}`);
                if (!bookingResponse.ok) throw new Error('Failed to fetch booking details');
                const booking = await bookingResponse.json();

                // Fetch available agents
                const agentsResponse = await fetch('/staff/api/receptionist/available-agents');
                if (!agentsResponse.ok) throw new Error('Failed to fetch available agents');
                const agents = await agentsResponse.json();

                // Update modal with booking details
                document.getElementById('modalPatientName').textContent = booking.patient_name;
                document.getElementById('modalTestName').textContent = booking.test_name;
                document.getElementById('modalBookingId').textContent = booking.id;
                document.getElementById('modalBarcode').textContent = booking.barcode;
                document.getElementById('assignBookingId').value = booking.id;

                // Update agent select options
                const select = document.getElementById('agentSelect');
                select.innerHTML = '<option value="">Select an agent...</option>' +
                    agents.map(a => `<option value="${a.id}">${a.name} (${a.current_load} assignments)</option>`).join('');

                // Set default date to today
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('collectionDate').value = today;
                document.getElementById('collectionDate').min = today;

                // Open modal
                openModal('assignAgentModal');

                // Debug: Log modal state
                console.log('Assign agent modal opened');
                const modal = document.getElementById('assignAgentModal');
                const buttons = modal.querySelectorAll('button');
                console.log('Modal buttons found:', buttons.length);
                buttons.forEach((btn, index) => {
                    console.log(`Button ${index}:`, btn.textContent, 'visible:', btn.offsetHeight > 0);
                });
            } catch (error) {
                console.error('Error loading data for assignment:', error);
                showAlert('Error loading data for assignment', 'danger');
            }
        }

        async function sendReminder(bookingId) {
            try {
                const response = await fetch(`/staff/api/receptionist/booking/${bookingId}`);
                if (!response.ok) throw new Error('Failed to fetch booking details');
                const booking = await response.json();
                
                document.getElementById('reminderType').value = 'patient';
                document.getElementById('reminderRecipientId').value = booking.user_id;
                document.getElementById('reminderMessage').value = 
                    `Reminder: You have an appointment scheduled for ${booking.booking_date} at ${booking.appointment_time}.`;
                
                openModal('sendReminderModal');
            } catch (error) {
                console.error('Error loading booking details:', error);
                showAlert('Error loading booking details', 'danger');
            }
        }

        async function sendAgentReminder(agentId) {
            try {
                const response = await fetch(`/staff/api/receptionist/agent/${agentId}`);
                if (!response.ok) throw new Error('Failed to fetch agent details');
                const agent = await response.json();
                
                document.getElementById('reminderType').value = 'agent';
                document.getElementById('reminderRecipientId').value = agentId;
                document.getElementById('reminderMessage').value = 
                    `Reminder: You have ${agent.pending_collections} pending sample collections for today.`;
                
                openModal('sendReminderModal');
            } catch (error) {
                console.error('Error loading agent details:', error);
                showAlert('Error loading agent details', 'danger');
            }
        }

        // Modal Functions
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                if (modalId === 'uploadReportModal') {
                    loadAvailableBookings();
                }
                modal.style.display = 'block';
                modal.classList.add('active');
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('active');
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
                event.target.classList.remove('active');
            }
        }

        // Close modal when clicking the close button
        document.querySelectorAll('.modal .close').forEach(closeBtn => {
            closeBtn.onclick = function() {
                const modal = this.closest('.modal');
                if (modal) {
                    modal.style.display = 'none';
                    modal.classList.remove('active');
                }
            }
        });

        // Mobile sidebar toggle function
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('active');
        }

        // Show tab function
        function showTab(tabId) {
            // Hide all tab panes
            const tabPanes = document.querySelectorAll('.tab-pane');
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Show target tab pane
            const targetPane = document.getElementById(tabId);
            if (targetPane) {
                targetPane.classList.add('active');
            }

            // Update navigation active states
            const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-item');
            navLinks.forEach(link => {
                if (link.getAttribute('data-tab') === tabId) {
                    link.classList.add('active');
                } else {
                    link.classList.remove('active');
                }
            });

            // Load tab data
            loadTabData(tabId);
        }

        // Initialize navigation on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Add scroll effect to header
            const header = document.querySelector('.header');
            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            });

            // Add stagger animation to sidebar items
            const navLinks = document.querySelectorAll('.nav-links a');
            navLinks.forEach((link, index) => {
                link.style.animationDelay = `${index * 0.1}s`;
                link.style.animation = 'slideInLeft 0.6s ease-out forwards';
            });

            // Load initial dashboard data
            loadTabData('dashboard');
        });

        // Enhanced search functionality
        function clearSearch(inputId) {
            const input = document.getElementById(inputId);
            if (!input) return; // Exit if input doesn't exist

            const clearBtn = input.parentElement.querySelector('.clear-search');
            input.value = '';
            if (clearBtn) clearBtn.style.display = 'none';

            // Trigger search to show all results
            if (inputId === 'appointmentSearchInput') {
                loadAppointments();
            } else if (inputId === 'agentSearchInput') {
                loadPickupAgents();
            } else if (inputId === 'collectionSearchInput') {
                loadSampleCollections();
            } else if (inputId === 'reportSearchInput') {
                loadReports();
            } else if (inputId === 'verificationSearchInput') {
                loadVerificationStatus();
            }
        }

        // Show/hide clear button based on input
        function setupSearchInput(inputId) {
            const input = document.getElementById(inputId);
            if (!input) return; // Exit if input doesn't exist

            const clearBtn = input.parentElement.querySelector('.clear-search');
            if (!clearBtn) return; // Exit if clear button doesn't exist

            input.addEventListener('input', function() {
                if (this.value.length > 0) {
                    clearBtn.style.display = 'block';
                } else {
                    clearBtn.style.display = 'none';
                }
            });
        }

        // Clear all filters for a specific section
        function clearAllFilters(section) {
            if (section === 'appointments') {
                document.getElementById('appointmentSearchInput').value = '';
                document.getElementById('appointmentStatusFilter').value = '';
                document.getElementById('appointmentPaymentFilter').value = '';
                document.getElementById('appointmentDateFromFilter').value = '';
                document.getElementById('appointmentDateToFilter').value = '';

                // Hide clear search button
                const clearBtn = document.querySelector('#appointmentSearchInput').parentElement.querySelector('.clear-search');
                if (clearBtn) clearBtn.style.display = 'none';

                // Trigger filter update
                loadAppointments();
            } else if (section === 'agents') {
                document.getElementById('agentSearchInput').value = '';
                document.getElementById('agentStatusFilter').value = '';

                // Hide clear search button
                const clearBtn = document.querySelector('#agentSearchInput').parentElement.querySelector('.clear-search');
                if (clearBtn) clearBtn.style.display = 'none';

                // Trigger filter update
                loadPickupAgents();
            } else if (section === 'collections') {
                document.getElementById('collectionSearchInput').value = '';
                document.getElementById('collectionStatusFilter').value = '';
                document.getElementById('collectionAgentFilter').value = '';
                document.getElementById('collectionDateFilter').value = '';

                // Hide clear search button
                const clearBtn = document.querySelector('#collectionSearchInput').parentElement.querySelector('.clear-search');
                if (clearBtn) clearBtn.style.display = 'none';

                // Trigger filter update
                loadSampleCollections();
            } else if (section === 'reports') {
                document.getElementById('reportSearchInput').value = '';
                document.getElementById('reportStatusFilter').value = '';
                document.getElementById('sampleStatusFilter').value = '';

                // Hide clear search button
                const clearBtn = document.querySelector('#reportSearchInput').parentElement.querySelector('.clear-search');
                if (clearBtn) clearBtn.style.display = 'none';

                // Trigger filter update
                loadReports();
            } else if (section === 'verification') {
                document.getElementById('verificationSearchInput').value = '';
                document.getElementById('verificationStatusFilter').value = '';
                document.getElementById('verificationDateFilter').value = '';

                // Hide clear search button
                const clearBtn = document.querySelector('#verificationSearchInput').parentElement.querySelector('.clear-search');
                if (clearBtn) clearBtn.style.display = 'none';

                // Trigger filter update
                loadVerificationStatus();
            }
        }

        // Filter appointments function (alias for loadAppointments)
        function filterAppointments() {
            loadAppointments();
        }

        // Refresh functions
        function refreshPatientRecords() {
            showAlert('Refreshing patient records...', 'info');
            if (typeof loadAppointments === 'function') {
                loadAppointments();
            }
        }

        function exportPatientRecords() {
            showAlert('Exporting patient records...', 'info');
            // Add export functionality here
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const mobileToggle = document.querySelector('.mobile-toggle');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !mobileToggle.contains(event.target)) {
                sidebar.classList.remove('active');
            }
        });

        // Header scroll effect
        window.addEventListener('scroll', function() {
            const header = document.getElementById('header');
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (scrollTop > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });

        // Initialize the dashboard
        document.addEventListener('DOMContentLoaded', () => {
            loadTabData('dashboard');

            // Setup enhanced search inputs
            setupSearchInput('appointmentSearchInput');
            setupSearchInput('collectionSearchInput');
            setupSearchInput('agentSearchInput');
            setupSearchInput('reportSearchInput');
            setupSearchInput('verificationSearchInput');
        });

        // Add event listeners for search and filters
        document.addEventListener('DOMContentLoaded', () => {
            // Appointments search and filters
            const appointmentSearchInput = document.getElementById('appointmentSearchInput');
            const appointmentStatusFilter = document.getElementById('appointmentStatusFilter');
            const appointmentPaymentFilter = document.getElementById('appointmentPaymentFilter');
            const appointmentDateFromFilter = document.getElementById('appointmentDateFromFilter');
            const appointmentDateToFilter = document.getElementById('appointmentDateToFilter');

            // Add debounce function for search input
            if (appointmentSearchInput) {
                let searchTimeout;
                appointmentSearchInput.addEventListener('input', (e) => {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        loadAppointments();
                    }, 300);
                });
            }

            // Add event listeners for filters
            if (appointmentStatusFilter) appointmentStatusFilter.addEventListener('change', loadAppointments);
            if (appointmentPaymentFilter) appointmentPaymentFilter.addEventListener('change', loadAppointments);
            if (appointmentDateFromFilter) appointmentDateFromFilter.addEventListener('change', loadAppointments);
            if (appointmentDateToFilter) appointmentDateToFilter.addEventListener('change', loadAppointments);

            // Sample Collections search and filters
            const collectionSearchInput = document.getElementById('collectionSearchInput');
            const collectionStatusFilter = document.getElementById('collectionStatusFilter');
            const collectionAgentFilter = document.getElementById('collectionAgentFilter');
            const collectionDateFilter = document.getElementById('collectionDateFilter');

            // Add debounce function for search input
            if (collectionSearchInput) {
                let collectionSearchTimeout;
                collectionSearchInput.addEventListener('input', (e) => {
                    clearTimeout(collectionSearchTimeout);
                    collectionSearchTimeout = setTimeout(() => {
                        loadSampleCollections();
                    }, 300);
                });
            }

            // Add event listeners for filters
            if (collectionStatusFilter) collectionStatusFilter.addEventListener('change', loadSampleCollections);
            if (collectionAgentFilter) collectionAgentFilter.addEventListener('change', loadSampleCollections);
            if (collectionDateFilter) collectionDateFilter.addEventListener('change', loadSampleCollections);

            // Load available agents for the filter dropdown
            loadAvailableAgents();

            // Verification status search and filters
            const verificationSearchInput = document.getElementById('verificationSearchInput');
            const verificationStatusFilter = document.getElementById('verificationStatusFilter');
            const verificationDateFilter = document.getElementById('verificationDateFilter');

            // Add debounce function for search input
            if (verificationSearchInput) {
                let verificationSearchTimeout;
                verificationSearchInput.addEventListener('input', (e) => {
                    clearTimeout(verificationSearchTimeout);
                    verificationSearchTimeout = setTimeout(() => {
                        loadVerificationStatus();
                    }, 300);
                });
            }

            // Add event listeners for filters
            if (verificationStatusFilter) verificationStatusFilter.addEventListener('change', loadVerificationStatus);
            if (verificationDateFilter) verificationDateFilter.addEventListener('change', loadVerificationStatus);
        });

        // Load available agents for the filter dropdown
        async function loadAvailableAgents() {
            try {
                const response = await fetch('/staff/api/receptionist/available-agents');
                if (!response.ok) throw new Error('Failed to fetch pickup agents');
                const agents = await response.json();
                
                const agentFilter = document.getElementById('collectionAgentFilter');
                agentFilter.innerHTML = '<option value="">All Agents</option>' +
                    agents.map(a => `<option value="${a.id}">${a.name}</option>`).join('');
            } catch (error) {
                console.error('Error loading available agents:', error);
                showAlert('Error loading available agents', 'danger');
            }
        }

        // Schedule Pickup functionality
        async function schedulePickup(bookingId) {
            try {
                const response = await fetch('/staff/api/receptionist/available-agents');
                if (!response.ok) throw new Error('Failed to fetch available agents');
                const agents = await response.json();
                
                const select = document.getElementById('agentSelect');
                select.innerHTML = '<option value="">Select an agent...</option>' +
                    agents.map(a => `<option value="${a.id}">${a.name} (${a.current_load} assignments)</option>`).join('');
                
                document.getElementById('assignBookingId').value = bookingId;
                document.getElementById('collectionDate').value = new Date().toISOString().split('T')[0];
                openModal('assignAgentModal');
            } catch (error) {
                console.error('Error loading available agents:', error);
                showAlert('Error loading available agents', 'danger');
            }
        }

        // Update scheduleCollection function
        async function scheduleCollection(bookingId) {
            try {
                // Fetch booking details
                const bookingResponse = await fetch(`/staff/api/receptionist/booking/${bookingId}`);
                if (!bookingResponse.ok) throw new Error('Failed to fetch booking details');
                const booking = await bookingResponse.json();

                // Fetch available agents
                const agentsResponse = await fetch('/staff/api/receptionist/available-agents');
                if (!agentsResponse.ok) throw new Error('Failed to fetch available agents');
                const agents = await agentsResponse.json();

                // Update modal with booking details
                document.getElementById('schedulePatientName').textContent = booking.patient_name;
                document.getElementById('scheduleTestName').textContent = booking.test_name;
                document.getElementById('scheduleBookingId').textContent = booking.id;
                document.getElementById('scheduleBarcode').textContent = booking.barcode;
                document.getElementById('scheduleBookingIdInput').value = booking.id;

                // Update agent select options
                const select = document.getElementById('scheduleAgentSelect');
                select.innerHTML = '<option value="">Select an agent...</option>' +
                    agents.map(a => `<option value="${a.id}">${a.name} (${a.current_load} assignments)</option>`).join('');

                // Set default date to today
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('scheduleDate').value = today;
                document.getElementById('scheduleDate').min = today;

                // Open modal
                openModal('scheduleCollectionModal');
            } catch (error) {
                console.error('Error loading data for scheduling:', error);
                showAlert('Error loading data for scheduling', 'danger');
            }
        }

        // Add schedule collection form submission handler
        const scheduleCollectionForm = document.getElementById('scheduleCollectionForm');
        if (scheduleCollectionForm) {
            scheduleCollectionForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            try {
                const formData = new FormData(e.target);
                const data = {
                    booking_id: formData.get('booking_id'),
                    agent_id: formData.get('agent_id'),
                    collection_date: formData.get('collection_date'),
                    collection_time: formData.get('collection_time'),
                    notes: formData.get('notes')
                };

                const response = await fetch('/staff/api/receptionist/assign-agent', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) throw new Error('Failed to schedule collection');
                
                const result = await response.json();
                showAlert('Collection scheduled successfully', 'success');
                closeModal('scheduleCollectionModal');
                loadAppointments(); // Refresh the appointments table
            } catch (error) {
                console.error('Error scheduling collection:', error);
                showAlert('Error scheduling collection', 'danger');
            }
            });
        }

        // Add viewCollectionDetails function
        async function viewCollectionDetails(bookingId) {
            try {
                const response = await fetch(`/staff/api/receptionist/booking/${bookingId}`);
                if (!response.ok) throw new Error('Failed to fetch collection details');
                const details = await response.json();
                
                // Update modal with collection details
                document.getElementById('viewPatientName').textContent = details.patient_name;
                document.getElementById('viewTestName').textContent = details.test_name;
                document.getElementById('viewTestAmount').textContent = details.test_amount;
                document.getElementById('viewBookingId').textContent = details.id;
                document.getElementById('viewBarcode').textContent = details.barcode;
                document.getElementById('viewBookingDate').textContent = details.booking_date;
                document.getElementById('viewAppointmentTime').textContent = details.appointment_time;
                document.getElementById('viewBookingStatus').textContent = details.booking_status;
                document.getElementById('viewBookingStatus').className = `status-badge ${getBadgeClass(details.booking_status)}`;
                document.getElementById('viewAgentName').textContent = details.agent_name || 'Not Assigned';
                document.getElementById('viewCollectionStatus').textContent = details.collection_status || 'Pending';
                document.getElementById('viewCollectionStatus').className = `status-badge ${getBadgeClass(details.collection_status)}`;
                document.getElementById('viewPaymentStatus').textContent = details.payment_status || 'Pending';
                document.getElementById('viewPaymentStatus').className = `status-badge ${getBadgeClass(details.payment_status)}`;
                document.getElementById('viewPaymentAmount').textContent = details.payment_amount || '0.00';
                
                // Update address information
                document.getElementById('viewAddress').textContent = details.address || 'N/A';

                // Open modal
                openModal('viewCollectionDetailsModal');
            } catch (error) {
                console.error('Error loading collection details:', error);
                showAlert('Error loading collection details', 'danger');
            }
        }

        // Generate Professional Patient Bill
        function generatePatientBill() {
            const bookingId = document.getElementById('viewBookingId').textContent;
            if (!bookingId) {
                showAlert('No booking ID found', 'warning');
                return;
            }

            // Show loading message
            showAlert('Generating professional bill...', 'info');

            // Create a temporary link to download the PDF
            const downloadLink = document.createElement('a');
            downloadLink.href = `/staff/api/receptionist/generate-patient-bill/${bookingId}`;
            downloadLink.download = `CVBioLabs_Bill_${bookingId}.pdf`;
            downloadLink.style.display = 'none';

            // Add to document, click, and remove
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);

            // Show success message after a short delay
            setTimeout(() => {
                showAlert('Professional bill generated successfully!', 'success');
            }, 1000);
        }

        // Generate Professional Patient Bill (Direct from table)
        function generatePatientBillDirect(bookingId) {
            if (!bookingId) {
                showAlert('No booking ID found', 'warning');
                return;
            }

            // Show loading message
            showAlert('Generating professional bill...', 'info');

            // Create a temporary link to download the PDF
            const downloadLink = document.createElement('a');
            downloadLink.href = `/staff/api/receptionist/generate-patient-bill/${bookingId}`;
            downloadLink.download = `CVBioLabs_Bill_${bookingId}.pdf`;
            downloadLink.style.display = 'none';

            // Add to document, click, and remove
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);

            // Show success message after a short delay
            setTimeout(() => {
                showAlert('Professional bill generated successfully!', 'success');
            }, 1000);
        }

        // Print Collection Details
        function printCollectionDetails() {
            const modalContent = document.querySelector('#viewCollectionDetailsModal .collection-details');
            if (!modalContent) {
                showAlert('No details to print', 'warning');
                return;
            }

            // Create a new window for printing
            const printWindow = window.open('', '_blank');
            const patientName = document.getElementById('viewPatientName').textContent;
            const bookingId = document.getElementById('viewBookingId').textContent;

            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Collection Details - ${patientName}</title>
                    <style>
                        body {
                            font-family: 'Arial', sans-serif;
                            margin: 20px;
                            color: #333;
                            line-height: 1.6;
                        }
                        .header {
                            text-align: center;
                            border-bottom: 3px solid #f58220;
                            padding-bottom: 20px;
                            margin-bottom: 30px;
                        }
                        .header h1 {
                            color: #003865;
                            margin: 0;
                            font-size: 24px;
                        }
                        .header p {
                            color: #666;
                            margin: 5px 0 0 0;
                        }
                        .section {
                            margin-bottom: 25px;
                            padding: 15px;
                            border: 1px solid #e0e0e0;
                            border-radius: 8px;
                            background: #f9f9f9;
                        }
                        .section h3 {
                            color: #003865;
                            margin-top: 0;
                            margin-bottom: 15px;
                            font-size: 16px;
                            border-bottom: 1px solid #f58220;
                            padding-bottom: 5px;
                        }
                        .detail-row {
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: 8px;
                            padding: 5px 0;
                            border-bottom: 1px dotted #ddd;
                        }
                        .detail-row:last-child {
                            border-bottom: none;
                        }
                        .label {
                            font-weight: bold;
                            color: #003865;
                        }
                        .value {
                            color: #333;
                        }
                        .status-badge {
                            padding: 2px 8px;
                            border-radius: 12px;
                            font-size: 12px;
                            font-weight: bold;
                        }
                        .footer {
                            margin-top: 40px;
                            text-align: center;
                            font-size: 12px;
                            color: #666;
                            border-top: 1px solid #e0e0e0;
                            padding-top: 15px;
                        }
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>CV BIOLABS - Collection Details</h1>
                        <p>Booking ID: ${bookingId} | Patient: ${patientName}</p>
                    </div>

                    <div class="section">
                        <h3>Patient Information</h3>
                        <div class="detail-row">
                            <span class="label">Name:</span>
                            <span class="value">${document.getElementById('viewPatientName').textContent}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Test:</span>
                            <span class="value">${document.getElementById('viewTestName').textContent}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Amount:</span>
                            <span class="value">₹${document.getElementById('viewTestAmount').textContent}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Address:</span>
                            <span class="value">${document.getElementById('viewAddress').textContent}</span>
                        </div>
                    </div>

                    <div class="section">
                        <h3>Booking Information</h3>
                        <div class="detail-row">
                            <span class="label">Booking ID:</span>
                            <span class="value">${document.getElementById('viewBookingId').textContent}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Barcode:</span>
                            <span class="value">${document.getElementById('viewBarcode').textContent}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Date:</span>
                            <span class="value">${document.getElementById('viewBookingDate').textContent}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Time:</span>
                            <span class="value">${document.getElementById('viewAppointmentTime').textContent}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Status:</span>
                            <span class="value">${document.getElementById('viewBookingStatus').textContent}</span>
                        </div>
                    </div>

                    <div class="section">
                        <h3>Collection Information</h3>
                        <div class="detail-row">
                            <span class="label">Agent:</span>
                            <span class="value">${document.getElementById('viewAgentName').textContent}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Collection Status:</span>
                            <span class="value">${document.getElementById('viewCollectionStatus').textContent}</span>
                        </div>
                    </div>

                    <div class="section">
                        <h3>Payment Information</h3>
                        <div class="detail-row">
                            <span class="label">Status:</span>
                            <span class="value">${document.getElementById('viewPaymentStatus').textContent}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Amount:</span>
                            <span class="value">₹${document.getElementById('viewPaymentAmount').textContent}</span>
                        </div>
                    </div>

                    <div class="footer">
                        <p>Generated on ${new Date().toLocaleString()}</p>
                        <p>CV BIOLABS - Professional Healthcare Services</p>
                    </div>
                </body>
                </html>
            `);

            printWindow.document.close();
            printWindow.focus();

            // Wait for content to load then print
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 250);
        }

        // Add assign agent form submission handler
        document.addEventListener('DOMContentLoaded', function() {
            const assignAgentForm = document.getElementById('assignAgentForm');
            if (assignAgentForm) {
                assignAgentForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    console.log('Assign agent form submitted');

                    try {
                        const formData = new FormData(e.target);
                        const data = {
                            booking_id: formData.get('booking_id'),
                            agent_id: formData.get('agent_id'),
                            collection_date: formData.get('collection_date')
                        };

                        console.log('Form data:', data);

                        // Validate required fields
                        if (!data.booking_id || !data.agent_id || !data.collection_date) {
                            showAlert('Please fill in all required fields', 'warning');
                            return;
                        }

                        const csrfToken = document.getElementById('csrf_token').value;
                        const response = await fetch('/staff/api/receptionist/assign-agent', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRFToken': csrfToken
                            },
                            body: JSON.stringify(data)
                        });

                        if (!response.ok) throw new Error('Failed to assign agent');
                        const result = await response.json();
                        showAlert('Agent assigned successfully', 'success');
                        closeModal('assignAgentModal');
                        loadAppointments(); // Refresh the appointments table
                    } catch (error) {
                        console.error('Error assigning agent:', error);
                        showAlert('Error assigning agent', 'danger');
                    }
                });
            } else {
                console.error('Assign agent form not found');
            }
        });

        // Report Management Functions
        let currentReportId = null;

        // Add Report
        async function addReport(event) {
            event.preventDefault();
            try {
                const form = event.target;
                const formData = new FormData(form);

                const response = await fetch('/staff/api/reports/add', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) throw new Error('Failed to add report');
                
                const result = await response.json();
                showAlert('Report added successfully', 'success');
                closeModal('addReportModal');
                loadReports();
            } catch (error) {
                console.error('Error adding report:', error);
                showAlert('Error adding report', 'danger');
            }
        }

        // View Report
        async function viewReport(reportId) {
            try {
                currentReportId = reportId;
                const response = await fetch(`/staff/api/reports/${reportId}`);
                if (!response.ok) throw new Error('Failed to fetch report details');
                const report = await response.json();

                const reportDetails = document.getElementById('reportDetails');
                reportDetails.innerHTML = `
                    <div class="report-details">
                        <p><strong>Patient:</strong> ${report.patient_name}</p>
                        <p><strong>Test:</strong> ${report.test_name}</p>
                        <p><strong>Date:</strong> ${report.created_at}</p>
                        <p><strong>Type:</strong> ${report.report_type}</p>
                        <p><strong>Status:</strong> <span class="status-badge ${getBadgeClass(report.report_status)}">${report.report_status}</span></p>
                        <p><strong>Doctor:</strong> ${report.doctor_name || 'Not Assigned'}</p>
                        ${report.doctor_review ? `<p><strong>Doctor Review:</strong> ${report.doctor_review}</p>` : ''}
                        ${report.comments ? `<p><strong>Comments:</strong> ${report.comments}</p>` : ''}
                    </div>
                `;

                openModal('viewReportModal');
            } catch (error) {
                console.error('Error viewing report:', error);
                showAlert('Error viewing report', 'danger');
            }
        }

        // Download Report
        async function downloadReport(reportId) {
            try {
                const response = await fetch(`/staff/api/reports/${reportId}/download`);
                if (!response.ok) throw new Error('Failed to download report');
                
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `report_${reportId}.pdf`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                a.remove();
            } catch (error) {
                console.error('Error downloading report:', error);
                showAlert('Error downloading report', 'danger');
            }
        }

        // Delete Report
        async function deleteReport(reportId) {
            if (!confirm('Are you sure you want to delete this report?')) return;

            try {
                const response = await fetch(`/staff/api/reports/${reportId}/delete`, {
                    method: 'POST'
                });

                if (!response.ok) throw new Error('Failed to delete report');
                
                showAlert('Report deleted successfully', 'success');
                closeModal('viewReportModal');
                loadReports();
            } catch (error) {
                console.error('Error deleting report:', error);
                showAlert('Error deleting report', 'danger');
            }
        }

        // Export Reports
        async function handleExportReports(event) {
            event.preventDefault();
            try {
                const startDate = document.getElementById('exportStartDate').value;
                const endDate = document.getElementById('exportEndDate').value;
                const type = document.getElementById('exportType').value;

                if (!startDate || !endDate) {
                    showAlert('Please select both start and end dates', 'warning');
                    return;
                }

                const response = await fetch(`/staff/api/reports/export?start_date=${startDate}&end_date=${endDate}&type=${type}`);
                if (!response.ok) throw new Error('Failed to export reports');
                
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `reports_${startDate}_to_${endDate}.xlsx`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                a.remove();
                
                closeModal('exportReportsModal');
            } catch (error) {
                console.error('Error exporting reports:', error);
                showAlert('Error exporting reports', 'danger');
            }
        }

        // Add event listeners for report management
        document.addEventListener('DOMContentLoaded', () => {
            // Add Report form submission
            const addReportForm = document.getElementById('addReportForm');
            if (addReportForm) {
                addReportForm.addEventListener('submit', addReport);
            }

            // Export Reports form submission
            const exportReportsForm = document.getElementById('exportReportsForm');
            if (exportReportsForm) {
                exportReportsForm.addEventListener('submit', handleExportReports);
            }

            // Report search and filters
            const reportSearchInput = document.getElementById('reportSearchInput');
            const reportStatusFilter = document.getElementById('reportStatusFilter');
            const reportTypeFilter = document.getElementById('reportTypeFilter');
            const reportStartDate = document.getElementById('reportStartDate');
            const reportEndDate = document.getElementById('reportEndDate');

            // Add debounce function for search input
            if (reportSearchInput) {
                let searchTimeout;
                reportSearchInput.addEventListener('input', (e) => {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        loadReports();
                    }, 300);
                });
            }

            // Add event listeners for filters
            if (reportStatusFilter) reportStatusFilter.addEventListener('change', loadReports);
            if (reportTypeFilter) reportTypeFilter.addEventListener('change', loadReports);
            if (reportStartDate) reportStartDate.addEventListener('change', loadReports);
            if (reportEndDate) reportEndDate.addEventListener('change', loadReports);

            // Load doctors for the doctor select dropdown
            loadDoctors();
        });

        // Load Doctors
        async function loadDoctors() {
            try {
                const response = await fetch('/staff/api/receptionist/doctors');
                if (!response.ok) throw new Error('Failed to fetch doctors');
                const doctors = await response.json();
                
                const doctorSelect = document.getElementById('doctor_id');
                doctorSelect.innerHTML = '<option value="">Select Doctor</option>' +
                    doctors.map(d => `<option value="${d.id}">${d.name}</option>`).join('');
            } catch (error) {
                console.error('Error loading doctors:', error);
                showAlert('Error loading doctors', 'danger');
            }
        }

        // Handle report upload
        const uploadReportForm = document.getElementById('uploadReportForm');
        if (uploadReportForm) {
            uploadReportForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            try {
                const formData = new FormData(e.target);
                const csrfToken = document.querySelector('input[name="csrf_token"]').value;
                const response = await fetch('/staff/api/receptionist/upload-report', {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': csrfToken
                    },
                    body: formData
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to upload report');
                }

                const result = await response.json();
                showAlert('Report uploaded successfully', 'success');
                closeModal('uploadReportModal');
                loadReports();
            } catch (error) {
                console.error('Error uploading report:', error);
                showAlert(error.message || 'Error uploading report', 'danger');
            }
            });
        }

        // Handle report assignment
        const assignReportForm = document.getElementById('assignReportForm');
        if (assignReportForm) {
            assignReportForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            showLoader();
            try {
                const formData = new FormData(e.target);
                const data = {
                    report_id: formData.get('report_id'),
                    doctor_id: formData.get('doctor_id')
                };
                const csrfToken = document.getElementById('csrf_token').value;
                const response = await fetch('/staff/api/reports/assign', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) throw new Error('Failed to assign report');
                
                const result = await response.json();
                showAlert('Report assigned successfully', 'success');
                closeModal('assignReportModal');
                loadReports();
            } catch (error) {
                console.error('Error assigning report:', error);
                showAlert('Error assigning report', 'danger');
            } finally {
                hideLoader();
            }
            });
        }

        // Load doctors for assignment
        async function loadDoctors() {
            try {
                const response = await fetch('/staff/api/receptionist/doctors');
                if (!response.ok) throw new Error('Failed to fetch doctors');
                const doctors = await response.json();
                
                const doctorSelect = document.getElementById('doctorSelect');
                doctorSelect.innerHTML = '<option value="">Select a doctor...</option>' +
                    doctors.map(d => `<option value="${d.id}">${d.name} (${d.specialization})</option>`).join('');
            } catch (error) {
                console.error('Error loading doctors:', error);
                showAlert('Error loading doctors', 'danger');
            }
        }

        // Open assign modal
        async function openAssignModal(reportId) {
            document.getElementById('assignReportId').value = reportId;
            await loadDoctors();
            openModal('assignReportModal');
        }

        // Add this function before the DOMContentLoaded event listener
        async function loadAvailableBookings() {
            try {
                const response = await fetch('/staff/api/reports/available-bookings');
                if (!response.ok) throw new Error('Failed to fetch available bookings');
                availableBookings = await response.json();
                populateBookingsDropdown(availableBookings);
                showBookingSearchResults(availableBookings);
            } catch (error) {
                console.error('Error loading available bookings:', error);
                showAlert('Error loading available bookings', 'danger');
            }
        }

        function populateBookingsDropdown(bookings) {
            const bookingSelect = document.getElementById('uploadBookingId');
            bookingSelect.innerHTML = '<option value="">Select a booking...</option>' +
                bookings.map(b => `<option value="${b.id}">${b.id} - ${b.patient_name} (${b.TestName})</option>`).join('');
        }

        function filterBookingsDropdown() {
            const search = document.getElementById('bookingSearch').value.toLowerCase();
            const filtered = availableBookings.filter(b =>
                (b.patient_name && b.patient_name.toLowerCase().includes(search)) ||
                (b.TestName && b.TestName.toLowerCase().includes(search)) ||
                (b.booking_date && b.booking_date.toLowerCase().includes(search))
            );
            populateBookingsDropdown(filtered);
            showBookingSearchResults(filtered);
        }

        function showBookingSearchResults(bookings) {
            let resultsDiv = document.getElementById('bookingSearchResults');
            if (!resultsDiv) return;
            if (bookings.length === 0) {
                resultsDiv.innerHTML = '<div style="color:#888;">No results found.</div>';
                return;
            }
            resultsDiv.innerHTML = bookings.map(b =>
                `<div style="padding:4px 0;cursor:pointer; border-bottom:1px solid #eee;" onclick="selectBookingFromResult(${b.id})">
                    <strong>${b.patient_name}</strong> | ${b.TestName} | ${b.booking_date}
                </div>`
            ).join('');
            resultsDiv.style.display = 'block';
        }

        function selectBookingFromResult(bookingId) {
            document.getElementById('uploadBookingId').value = bookingId;
            // Hide the results after selection
            const resultsDiv = document.getElementById('bookingSearchResults');
            if (resultsDiv) resultsDiv.style.display = 'none';
        }

        // Hide booking search results when clicking outside
        window.addEventListener('click', function(event) {
            const resultsDiv = document.getElementById('bookingSearchResults');
            const searchInput = document.getElementById('bookingSearch');
            if (resultsDiv && !resultsDiv.contains(event.target) && event.target !== searchInput) {
                resultsDiv.style.display = 'none';
            }
        });

        // Call loadAvailableBookings when opening the modal
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                if (modalId === 'uploadReportModal') {
                    loadAvailableBookings();
                }
                modal.style.display = 'block';
                modal.classList.add('active');
            }
        }

        // View Agent Details
        async function viewAgentDetails(agentId) {
            try {
                const response = await fetch(`/staff/api/receptionist/agent/${agentId}`);
                if (!response.ok) throw new Error('Failed to fetch agent details');
                const agent = await response.json();
                
                // Update modal with agent details
                document.getElementById('viewAgentName').textContent = agent.name;
                document.getElementById('viewAgentPhone').textContent = agent.phone;
                document.getElementById('viewAgentEmail').textContent = agent.email || 'N/A';
                document.getElementById('viewAgentStatus').textContent = agent.status;
                document.getElementById('viewAgentStatus').className = `status-badge ${getBadgeClass(agent.status)}`;
                document.getElementById('viewAgentLoad').textContent = agent.current_load || '0';
                document.getElementById('viewAgentTotalCollections').textContent = agent.total_collections || '0';
                document.getElementById('viewAgentSuccessRate').textContent = agent.success_rate ? `${agent.success_rate}%` : 'N/A';

                // Update schedule section
                const scheduleHtml = agent.today_schedule && agent.today_schedule.length > 0 
                    ? agent.today_schedule.map(schedule => `
                        <p>
                            <strong>Time:</strong> ${schedule.time}<br>
                            <strong>Patient:</strong> ${schedule.patient_name}<br>
                            <strong>Status:</strong> <span class="status-badge ${getBadgeClass(schedule.status)}">${schedule.status}</span>
                        </p>
                    `).join('')
                    : '<p>No collections scheduled for today</p>';
                document.getElementById('viewAgentSchedule').innerHTML = scheduleHtml;

                openModal('viewAgentDetailsModal');
            } catch (error) {
                console.error('Error loading agent details:', error);
                showAlert('Error loading agent details', 'danger');
            }
        }

        // Update Agent Status
        async function updateAgentStatus(agentId) {
            try {
                const response = await fetch(`/staff/api/receptionist/agent/${agentId}`);
                if (!response.ok) throw new Error('Failed to fetch agent details');
                const agent = await response.json();
                
                document.getElementById('updateAgentId').value = agentId;
                document.getElementById('agentStatus').value = agent.status;
                document.getElementById('statusNotes').value = '';
                
                openModal('updateAgentStatusModal');
            } catch (error) {
                console.error('Error loading agent details:', error);
                showAlert('Error loading agent details', 'danger');
            }
        }

        // Add event listener for update agent status form
        const updateAgentStatusForm = document.getElementById('updateAgentStatusForm');
        if (updateAgentStatusForm) {
            updateAgentStatusForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            try {
                const formData = new FormData(e.target);
                const data = {
                    agent_id: formData.get('agent_id'),
                    status: formData.get('status'),
                    notes: formData.get('notes')
                };

                const response = await fetch('/staff/api/receptionist/update-agent-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) throw new Error('Failed to update agent status');
                
                const result = await response.json();
                showAlert('Agent status updated successfully', 'success');
                closeModal('updateAgentStatusModal');
                loadPickupAgents(); // Refresh the agents table
            } catch (error) {
                console.error('Error updating agent status:', error);
                showAlert('Error updating agent status', 'danger');
            }
            });
        }

let availableBookings = [];

async function loadAvailableBookings() {
    try {
        const response = await fetch('/staff/api/reports/available-bookings');
        if (!response.ok) throw new Error('Failed to fetch available bookings');
        availableBookings = await response.json();
        showBookingSearchResults(availableBookings);
    } catch (error) {
        console.error('Error loading available bookings:', error);
        showAlert('Error loading available bookings', 'danger');
    }
}

function filterBookingsDropdown() {
    const search = document.getElementById('bookingSearch').value.toLowerCase();
    const filtered = availableBookings.filter(b =>
        (b.patient_name && b.patient_name.toLowerCase().includes(search)) ||
        (b.TestName && b.TestName.toLowerCase().includes(search)) ||
        (b.booking_date && b.booking_date.toLowerCase().includes(search))
    ).slice(0, 15); // Show only top 15 results
    showBookingSearchResults(filtered);
}

function showBookingSearchResults(bookings) {
    let resultsDiv = document.getElementById('bookingSearchResults');
    if (!resultsDiv) return;
    if (bookings.length === 0) {
        resultsDiv.innerHTML = '<div style="color:#888;">No results found.</div>';
        resultsDiv.style.display = 'block';
        return;
    }
    resultsDiv.innerHTML = bookings.map(b =>
        `<div style="padding:8px 12px;cursor:pointer; border-bottom:1px solid #eee;" 
            onclick="selectBookingFromResult(${b.id}, '${escapeHtml(b.patient_name)}', '${escapeHtml(b.TestName)}', '${b.booking_date}')">
            <strong>${b.patient_name}</strong> | ${b.TestName} | ${b.booking_date}
        </div>`
    ).join('');
    resultsDiv.style.display = 'block';
}

// Utility to escape HTML for safe rendering
function escapeHtml(text) {
    if (!text) return '';
    return text.replace(/&/g, "&amp;")
               .replace(/</g, "&lt;")
               .replace(/>/g, "&gt;")
               .replace(/"/g, "&quot;")
               .replace(/'/g, "&#039;");
}

function selectBookingFromResult(bookingId, patientName, testName, bookingDate) {
    document.getElementById('bookingSearch').value = `${patientName} | ${testName} | ${bookingDate}`;
    document.getElementById('uploadBookingId').value = bookingId;
    const resultsDiv = document.getElementById('bookingSearchResults');
    if (resultsDiv) resultsDiv.style.display = 'none';
}

// Hide booking search results when clicking outside
window.addEventListener('click', function(event) {
    const resultsDiv = document.getElementById('bookingSearchResults');
    const searchInput = document.getElementById('bookingSearch');
    if (resultsDiv && !resultsDiv.contains(event.target) && event.target !== searchInput) {
        resultsDiv.style.display = 'none';
    }
});

// When opening the modal, load bookings and clear previous selection
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        if (modalId === 'uploadReportModal') {
            loadAvailableBookings();
            document.getElementById('bookingSearch').value = '';
            document.getElementById('uploadBookingId').value = '';
            document.getElementById('bookingSearchResults').innerHTML = '';
        }
        modal.style.display = 'block';
        modal.classList.add('active');
    }
}
        // ...existing code...

        // Update send report to patient AJAX handler to include CSRF token
        async function sendReportToPatient(reportId) {
            if (!confirm('Send this report to the patient?')) return;
            try {
                const response = await fetch('/staff/api/reports/send-to-patient', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                        // Removed CSRF token since endpoint is now exempt
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify({ report_id: parseInt(reportId) })
                });
                const result = await response.json();
                if (result.success) {
                    showAlert('Report sent to patient successfully', 'success');
                    loadReports();
                } else {
                    showAlert(result.error || 'Failed to send report', 'danger');
                }
            } catch (error) {
                showAlert('Error sending report', 'danger');
            }
        }

        // Duplicate function removed - using the updated version above
    </script>
<!-- Send Report to Patient Modal -->
<div class="modal" id="sendReportToPatientModal">
  <div class="modal-content">
    <span class="close" onclick="closeModal('sendReportToPatientModal')">&times;</span>
    <h2>Send Report to Patient</h2>
    <div class="form-group">
      <label>Patient Name:</label> <span id="modal-patient-name"></span>
    </div>
    <div class="form-group">
      <label>Email:</label> <span id="modal-patient-email"></span>
    </div>
    <div class="form-group">
      <label>Test Name:</label> <span id="modal-test-name"></span>
    </div>
    <div class="form-group">
      <label>Report:</label> <a id="modal-report-link" href="#" target="_blank">Download</a>
    </div>
    <div class="form-group">
      <label>Doctor Review:</label> <span id="modal-doctor-review"></span>
    </div>
    <div class="form-buttons">
      <button type="button" class="btn btn-secondary" onclick="closeModal('sendReportToPatientModal')">Close</button>
      <button type="button" class="btn btn-primary" id="modal-send-btn">Send</button>
    </div>
  </div>
</div>

<script>
// ...existing code...
// Open Send Report Modal and preload info
async function openSendReportToPatientModal(reportId) {
    const modal = document.getElementById('sendReportToPatientModal');
    modal.classList.add('active');
    // Fetch report details
    try {
        const resp = await fetch(`/staff/api/reports/${reportId}`);
        const data = await resp.json();
        if (data.error) {
            showAlert(data.error, 'danger');
            closeModal('sendReportToPatientModal');
            return;
        }
        document.getElementById('modal-patient-name').textContent = data.patient_name;
        document.getElementById('modal-patient-email').textContent = data.email;
        document.getElementById('modal-test-name').textContent = data.TestName;
        document.getElementById('modal-report-link').href = `/staff/uploads/reports/${data.report_url}`;
        document.getElementById('modal-doctor-review').textContent = data.doctor_review || 'N/A';
        document.getElementById('modal-report-link').textContent = 'Download Report';
        modal.dataset.reportId = reportId;
    } catch (e) {
        showAlert('Failed to load report details', 'danger');
        closeModal('sendReportToPatientModal');
    }
}
// Send report to patient from modal
async function sendReportToPatientFromModal() {
    showLoader();
    const modal = document.getElementById('sendReportToPatientModal');
    const reportId = modal.dataset.reportId;

    // Enhanced debugging and validation
    console.log('Sending report to patient:', {
        reportId: reportId,
        reportIdType: typeof reportId
    });

    // Validate reportId
    if (!reportId) {
        showAlert('No report ID found. Please try again.', 'danger');
        hideLoader();
        return;
    }

    // Ensure reportId is numeric
    const numericReportId = parseInt(reportId);
    if (isNaN(numericReportId)) {
        showAlert('Invalid report ID. Please try again.', 'danger');
        hideLoader();
        return;
    }

    try {
        const requestBody = { report_id: numericReportId };
        console.log('Request body:', requestBody);

        const response = await fetch('/staff/api/reports/send-to-patient', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
                // Removed CSRF token since endpoint is now exempt
            },
            credentials: 'same-origin',
            body: JSON.stringify(requestBody)
        });

        console.log('Response status:', response.status);

        const result = await response.json();
        console.log('Response data:', result);

        if (response.ok && result.success) {
            showAlert('Report sent to patient successfully', 'success');
            closeModal('sendReportToPatientModal');
            // Refresh both reports and verification status tables
            if (typeof loadReports === 'function') loadReports();
            if (typeof loadVerificationStatus === 'function') loadVerificationStatus();
        } else {
            showAlert(result.error || 'Failed to send report', 'danger');
        }
    } catch (error) {
        console.error('Error sending report:', error);
        showAlert('Error sending report: ' + error.message, 'danger');
    } finally {
        hideLoader();
    }
}
document.getElementById('modal-send-btn').addEventListener('click', sendReportToPatientFromModal);
// Utility to close modal
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('active');
    }
}
// To use: call openSendReportToPatientModal(reportId) when Send button is clicked for a report
// ...existing code...
</script>

<!-- Add this modal at the end of the body, before the closing body tag -->
<div class="modal" id="reportsToSendModal">
    <div class="modal-content">
        <span class="close" onclick="closeModal('reportsToSendModal')">&times;</span>
        <h2>Reports To Send</h2>
        <div class="search-filter-section">
            <div class="search-box">
                <input type="text" id="reportsToSendSearch" class="form-control" placeholder="Search by patient name or test...">
            </div>
        </div>
        <div class="table-responsive">
            <table>
                <thead>
                    <tr>
                        <th>Patient Name</th>
                        <th>Test</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="reportsToSendTable"></tbody>
            </table>
        </div>
    </div>
</div>

<script>
// Add these functions to your existing JavaScript
async function showReportsToSend() {
    try {
        const response = await fetch('/staff/api/receptionist/reports-to-send');
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to fetch reports to send');
        }
        const reports = await response.json();
        
        const tbody = document.getElementById('reportsToSendTable');
        if (!reports || reports.length === 0) {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center">No reports to send</td></tr>';
        } else {
            tbody.innerHTML = reports.map(report => `
                <tr>
                    <td>${report.patient_name || 'N/A'}</td>
                    <td>${report.test_name || 'N/A'}</td>
                    <td>${report.created_at ? new Date(report.created_at).toLocaleDateString() : 'N/A'}</td>
                    <td>
                        <div class="action-buttons">
                            ${report.report_url ? 
                                `<button class="btn btn-sm btn-info" onclick="viewReport(${report.id})">
                                    <i class="fas fa-eye"></i> View
                                </button>` : 
                                '<span class="text-muted">No Report</span>'
                            }
                            <button class="btn btn-sm btn-success" onclick="sendReportToPatient(${report.id})" ${!report.report_url ? 'disabled' : ''}>
                                <i class="fas fa-paper-plane"></i> Send
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }
        
        openModal('reportsToSendModal');
    } catch (error) {
        console.error('Error loading reports to send:', error);
        showAlert(error.message || 'Error loading reports to send', 'danger');
    }
}

// Add search functionality for reports to send
document.getElementById('reportsToSendSearch').addEventListener('input', async (e) => {
    const searchTerm = e.target.value.toLowerCase();
    const rows = document.querySelectorAll('#reportsToSendTable tr');
    
    rows.forEach(row => {
        const patientName = row.cells[0]?.textContent.toLowerCase() || '';
        const testName = row.cells[1]?.textContent.toLowerCase() || '';
        
        if (patientName.includes(searchTerm) || testName.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

</script>



<!-- Loader Overlay -->
<div id="loaderOverlay" style="display:none;position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:3000;background:rgba(255,255,255,0.7);justify-content:center;align-items:center;">
  <div style="border:6px solid #f3f3f3;border-top:6px solid #f47c20;border-radius:50%;width:60px;height:60px;animation:spin 1s linear infinite;"></div>
</div>
<style>
@keyframes spin { 0% { transform: rotate(0deg);} 100% { transform: rotate(360deg);} }
</style>
</body>
</html>